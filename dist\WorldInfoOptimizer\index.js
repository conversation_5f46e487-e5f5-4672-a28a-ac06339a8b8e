var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ () => {
    eval("{// ==UserScript==\n// @name         世界书优化器 (World Info Optimizer)\n// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0\n// @match        */*\n// @version      1.0.0\n// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。\n// <AUTHOR> & AI Assistant\n// @grant        none\n// @inject-into  content\n// ==/UserScript==\n\n// 使用IIFE封装，避免全局污染\n(() => {\n    console.log('[WorldInfoOptimizer] Script execution started.');\n    // --- 配置常量 ---\n    const SCRIPT_VERSION_TAG = 'v1_0_0';\n    const PANEL_ID = 'world-info-optimizer-panel';\n    const BUTTON_ID = 'world-info-optimizer-button';\n    const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\n    const BUTTON_TOOLTIP = '世界书优化器';\n    const BUTTON_TEXT_IN_MENU = '世界书优化器';\n    const SEARCH_INPUT_ID = 'wio-search-input';\n    const REFRESH_BTN_ID = 'wio-refresh-btn';\n    const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\n    const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\n    const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n    const LOREBOOK_OPTIONS = {\n        position: {\n            before_character_definition: '角色定义前',\n            after_character_definition: '角色定义后',\n            before_example_messages: '聊天示例前',\n            after_example_messages: '聊天示例后',\n            before_author_note: '作者笔记前',\n            after_author_note: '作者笔记后',\n            at_depth_as_system: '@D ⚙ 系统',\n            at_depth_as_assistant: '@D 🗨️ 角色',\n            at_depth_as_user: '@D 👤 用户',\n        },\n        logic: {\n            and_any: '任一 AND',\n            and_all: '所有 AND',\n            not_any: '任一 NOT',\n            not_all: '所有 NOT',\n        },\n    };\n    // --- 应用程序状态 ---\n    const appState = {\n        regexes: { global: [], character: [] },\n        lorebooks: { character: [] },\n        chatLorebook: null,\n        allLorebooks: [],\n        lorebookEntries: new Map(),\n        lorebookUsage: new Map(),\n        activeTab: 'global-lore',\n        isDataLoaded: false,\n        searchFilters: { bookName: true, entryName: true, keywords: true, content: true },\n        multiSelectMode: false,\n        selectedItems: new Set(),\n    };\n    // --- 全局变量 ---\n    let parentWin;\n    let $;\n    let TavernHelper;\n    /**\n     * 等待DOM和API就绪\n     */\n    function onReady(callback) {\n        const domSelector = '#extensionsMenu';\n        const maxRetries = 100;\n        let retries = 0;\n        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element \"${domSelector}\" AND core APIs.`);\n        const interval = setInterval(() => {\n            const parentDoc = window.parent.document;\n            parentWin = window.parent;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            if (domReady && apiReady) {\n                clearInterval(interval);\n                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);\n                try {\n                    callback(parentWin.jQuery, parentWin.TavernHelper);\n                }\n                catch (e) {\n                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);\n                }\n            }\n            else {\n                retries++;\n                if (retries > maxRetries) {\n                    clearInterval(interval);\n                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);\n                    if (!domReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element \"${domSelector}\" not found.`);\n                    if (!apiReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);\n                }\n            }\n        }, 150);\n    }\n    /**\n     * 错误处理包装器\n     */\n    const errorCatched = (fn, context = 'WorldInfoOptimizer') => async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                await showModal({\n                    type: 'alert',\n                    title: '脚本异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n        }\n    };\n    // --- 安全访问 lorebookEntries 的函数 ---\n    const safeGetLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.get !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            const entries = appState.lorebookEntries.get(bookName);\n            return Array.isArray(entries) ? entries : [];\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return [];\n        }\n    };\n    const safeSetLorebookEntries = (bookName, entries) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.set !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n    };\n    const safeDeleteLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.delete !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.delete(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeClearLorebookEntries = () => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.clear !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.clear();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeHasLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            if (typeof appState.lorebookEntries.has !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            return appState.lorebookEntries.has(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return false;\n        }\n    };\n    // --- 工具函数 ---\n    const escapeHtml = (text) => {\n        if (typeof text !== 'string')\n            return String(text);\n        const div = document.createElement('div');\n        div.textContent = text;\n        return div.innerHTML;\n    };\n    // 处理文本内容并保留换行符\n    const escapeHtmlWithNewlines = (text) => {\n        const escaped = escapeHtml(text);\n        return escaped.replace(/\\n/g, '<br>');\n    };\n    const unescapeHtml = (html) => {\n        if (typeof html !== 'string')\n            return String(html);\n        const area = document.createElement('textarea');\n        area.innerHTML = html;\n        return area.value;\n    };\n    const highlightText = (text, searchTerm) => {\n        if (!text)\n            return '';\n        // 移除任何现有的高亮标签，防止嵌套\n        const cleanText = text.replace(/<\\/?mark\\s+class=\"wio-highlight\">/gi, '');\n        // 如果没有搜索词，只返回清理后的纯文本（不需要HTML转义）\n        if (!searchTerm.trim()) {\n            return cleanText;\n        }\n        // Escape special characters in search term for regex.\n        const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        try {\n            const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n            // Split the text by the search term, keeping the delimiter.\n            const parts = cleanText.split(regex);\n            // Process parts: escape normal text, wrap matches in <mark>.\n            return parts\n                .map((part, index) => {\n                if (!part)\n                    return '';\n                // The matched parts are at odd indices (1, 3, 5, ...).\n                if (index % 2 === 1) {\n                    return `<mark class=\"wio-highlight\">${escapeHtml(part)}</mark>`;\n                }\n                else {\n                    return escapeHtml(part);\n                }\n            })\n                .join('');\n        }\n        catch (e) {\n            console.warn(`[WorldInfoOptimizer] Invalid regex for highlighting: \"${escapedSearchTerm}\"`, e);\n            // Fallback to just escaping the text on error.\n            return escapeHtml(cleanText);\n        }\n    };\n    const isMatch = (text, searchTerm) => {\n        if (!searchTerm.trim())\n            return false;\n        if (!text)\n            return false;\n        try {\n            // 首先尝试简单的文本包含检查（对JSON更友好）\n            if (text.toLowerCase().includes(searchTerm.toLowerCase())) {\n                return true;\n            }\n            // 然后尝试正则匹配（处理转义后的搜索词）\n            // 转义所有正则特殊字符，包括引号\n            const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\\]\\\\\"]/g, '\\\\$&');\n            const regex = new RegExp(escapedSearchTerm, 'i');\n            return regex.test(text);\n        }\n        catch (e) {\n            console.warn(`[WorldInfoOptimizer] Invalid regex created from search term: \"${searchTerm}\"`, e);\n            // 作为备选方案，如果正则表达式无效，则退回到简单的文本搜索\n            return text.toLowerCase().includes(searchTerm.toLowerCase());\n        }\n    };\n    // --- 通知和模态框函数 ---\n    const showSuccessTick = (message = '操作成功', duration = 1500) => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return;\n        $panel.find('.wio-toast-notification').remove();\n        const toastHtml = `<div class=\"wio-toast-notification\"><i class=\"fa-solid fa-check-circle\"></i> ${escapeHtml(message)}</div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        setTimeout(() => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        }, duration);\n    };\n    const showProgressToast = (initialMessage = '正在处理...') => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return { update: () => { }, remove: () => { } };\n        $panel.find('.wio-progress-toast').remove();\n        const toastHtml = `<div class=\"wio-progress-toast\"><i class=\"fa-solid fa-spinner fa-spin\"></i> <span class=\"wio-progress-text\">${escapeHtml(initialMessage)}</span></div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        const update = (newMessage) => {\n            $toast.find('.wio-progress-text').html(escapeHtml(newMessage));\n        };\n        const remove = () => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        };\n        return { update, remove };\n    };\n    const showModal = (options) => {\n        return new Promise((resolve, reject) => {\n            const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;\n            let buttonsHtml = '';\n            if (type === 'alert')\n                buttonsHtml = '<button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            else if (type === 'confirm')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确认</button>';\n            else if (type === 'prompt')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            const inputHtml = type === 'prompt'\n                ? `<input type=\"text\" class=\"wio-modal-input\" placeholder=\"${escapeHtml(placeholder)}\" value=\"${escapeHtml(value)}\">`\n                : '';\n            const modalHtml = `<div class=\"wio-modal-overlay\"><div class=\"wio-modal-content\"><div class=\"wio-modal-header\">${escapeHtml(title)}</div><div class=\"wio-modal-body\"><p>${escapeHtmlWithNewlines(text)}</p>${inputHtml}</div><div class=\"wio-modal-footer\">${buttonsHtml}</div></div></div>`;\n            const $modal = $(modalHtml).hide();\n            const $panel = $(`#${PANEL_ID}`, parentWin.document);\n            if ($panel.length > 0) {\n                $panel.append($modal);\n            }\n            else {\n                $('body', parentWin.document).append($modal);\n            }\n            $modal.fadeIn(200);\n            const $input = $modal.find('.wio-modal-input');\n            if (type === 'prompt')\n                $input.focus().select();\n            const closeModal = (isSuccess, val) => {\n                $modal.fadeOut(200, () => {\n                    $modal.remove();\n                    if (isSuccess)\n                        resolve(val);\n                    else\n                        reject();\n                });\n            };\n            $modal.on('click', '.wio-modal-ok', () => {\n                const val = type === 'prompt' ? $input.val() : true;\n                if (type === 'prompt' && !String(val).trim()) {\n                    $input.addClass('wio-input-error');\n                    setTimeout(() => $input.removeClass('wio-input-error'), 500);\n                    return;\n                }\n                closeModal(true, val);\n            });\n            $modal.on('click', '.wio-modal-cancel', () => closeModal(false));\n            if (type === 'prompt') {\n                $input.on('keydown', (e) => {\n                    if (e.key === 'Enter')\n                        $modal.find('.wio-modal-ok').click();\n                    else if (e.key === 'Escape')\n                        closeModal(false);\n                });\n            }\n        });\n    };\n    // --- API 包装器 ---\n    let TavernAPI = null;\n    const initializeTavernAPI = () => {\n        TavernAPI = {\n            createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),\n            deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),\n            getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),\n            setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),\n            getCharData: errorCatched(async () => await TavernHelper.getCharData()),\n            Character: TavernHelper.Character || null,\n            getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),\n            replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),\n            getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),\n            getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),\n            getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),\n            getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),\n            getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),\n            setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),\n            getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),\n            setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),\n            createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),\n            deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),\n            saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),\n            setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),\n        };\n    };\n    // --- 数据加载函数 ---\n    const loadAllData = errorCatched(async () => {\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.html(`\r\n            <div class=\"wio-loading-container\">\r\n                <div class=\"wio-loading-title\">数据同步中...</div>\r\n                <div class=\"wio-loading-progress-bar-container\">\r\n                    <div id=\"wio-loading-bar\" class=\"wio-loading-progress-bar\" style=\"width: 0%;\"></div>\r\n                </div>\r\n                <div id=\"wio-loading-status\" class=\"wio-loading-status-text\">正在初始化...</div>\r\n            </div>\r\n        `);\n        const $progressBar = $('#wio-loading-bar', parentWin.document);\n        const $statusText = $('#wio-loading-status', parentWin.document);\n        const updateProgress = (percentage, text) => {\n            if ($progressBar.length)\n                $progressBar.css('width', `${Math.min(100, Math.max(0, percentage))}%`);\n            if ($statusText.length)\n                $statusText.text(text);\n        };\n        try {\n            updateProgress(5, '正在连接 SillyTavern API...');\n            // 防御性检查：确保SillyTavern API可用\n            if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {\n                console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');\n                appState.regexes.global = [];\n                appState.regexes.character = [];\n                appState.allLorebooks = [];\n                appState.lorebooks.character = [];\n                appState.chatLorebook = null;\n                safeClearLorebookEntries();\n                appState.isDataLoaded = true;\n                renderContent();\n                return;\n            }\n            const context = parentWin.SillyTavern.getContext() || {};\n            const allCharacters = Array.isArray(context.characters) ? context.characters : [];\n            const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n            const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n            let charData = null, charLinkedBooks = null, chatLorebook = null;\n            updateProgress(10, '正在获取核心设置...');\n            // 使用Promise.allSettled来避免单个失败影响整体\n            const promises = [\n                TavernAPI.getRegexes().catch(() => []),\n                TavernAPI.getLorebookSettings().catch(() => ({})),\n                TavernAPI.getLorebooks().catch(() => []),\n            ];\n            if (hasActiveCharacter) {\n                promises.push(TavernAPI.getCharData().catch(() => null));\n                promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));\n            }\n            else {\n                promises.push(Promise.resolve(null), Promise.resolve(null));\n            }\n            if (hasActiveChat) {\n                promises.push(TavernAPI.getChatLorebook().catch((error) => {\n                    console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);\n                    return null;\n                }));\n            }\n            else {\n                promises.push(Promise.resolve(null));\n            }\n            const results = await Promise.allSettled(promises);\n            updateProgress(20, '核心数据已获取，正在分析...');\n            // 安全提取结果\n            const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n            const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n            const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n            charData = results[3]?.status === 'fulfilled' ? results[3].value : null;\n            charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;\n            chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;\n            appState.regexes.global = Array.isArray(allUIRegexes)\n                ? allUIRegexes.filter((r) => r.scope === 'global')\n                : [];\n            updateCharacterRegexes(allUIRegexes, charData);\n            safeClearLorebookEntries();\n            appState.lorebookUsage.clear();\n            const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            updateProgress(30, '正在分析角色数据...');\n            // 安全处理角色世界书\n            if (Array.isArray(allCharacters) && allCharacters.length > 0) {\n                try {\n                    // No need for Promise.all here as it's not a bottleneck and makes progress tracking complex\n                    for (let i = 0; i < allCharacters.length; i++) {\n                        const char = allCharacters[i];\n                        if (!char || !char.name)\n                            continue;\n                        try {\n                            let books = null;\n                            try {\n                                const result = TavernHelper.getCharLorebooks({ name: char.name });\n                                books = result && typeof result.then === 'function' ? await result : result;\n                            }\n                            catch (error) {\n                                console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character \"${char.name}\":`, error);\n                            }\n                            if (books && typeof books === 'object') {\n                                const bookSet = new Set();\n                                if (books.primary && typeof books.primary === 'string')\n                                    bookSet.add(books.primary);\n                                if (Array.isArray(books.additional)) {\n                                    books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));\n                                }\n                                bookSet.forEach(bookName => {\n                                    if (typeof bookName === 'string') {\n                                        if (!appState.lorebookUsage.has(bookName)) {\n                                            appState.lorebookUsage.set(bookName, []);\n                                        }\n                                        appState.lorebookUsage.get(bookName).push(char.name);\n                                        knownBookNames.add(bookName);\n                                    }\n                                });\n                            }\n                        }\n                        catch (charError) {\n                            console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);\n                        }\n                        const charProgress = 30 + (i / allCharacters.length) * 20; // This stage takes 20%\n                        updateProgress(charProgress, `正在分析角色: ${char.name}`);\n                    }\n                }\n                catch (charProcessingError) {\n                    console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);\n                }\n            }\n            updateProgress(50, '角色数据分析完毕，正在整理世界书列表...');\n            const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);\n            appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({\n                name: name,\n                enabled: enabledGlobalBooks.has(name),\n            }));\n            const charBookSet = new Set();\n            if (charLinkedBooks && typeof charLinkedBooks === 'object') {\n                if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {\n                    charBookSet.add(charLinkedBooks.primary);\n                }\n                if (Array.isArray(charLinkedBooks.additional)) {\n                    charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));\n                }\n            }\n            appState.lorebooks.character = Array.from(charBookSet);\n            appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;\n            if (typeof chatLorebook === 'string') {\n                knownBookNames.add(chatLorebook);\n            }\n            const allBooksToLoad = Array.from(knownBookNames);\n            const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            updateProgress(55, `准备加载 ${allBooksToLoad.length} 个世界书的条目...`);\n            // 分批加载世界书条目\n            const batchSize = 5;\n            for (let i = 0; i < allBooksToLoad.length; i += batchSize) {\n                const batch = allBooksToLoad.slice(i, i + batchSize);\n                await Promise.allSettled(batch.map(async (name) => {\n                    if (existingBookFiles.has(name) && typeof name === 'string') {\n                        try {\n                            const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);\n                            safeSetLorebookEntries(name, entries);\n                        }\n                        catch (entryError) {\n                            console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);\n                        }\n                    }\n                }));\n                const bookProgress = 55 + ((i + batch.length) / allBooksToLoad.length) * 40; // This stage takes 40%\n                updateProgress(bookProgress, `已加载 ${i + batch.length} / ${allBooksToLoad.length} 个世界书`);\n            }\n            updateProgress(100, '加载完成，正在渲染界面...');\n            appState.isDataLoaded = true;\n            renderContent();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in loadAllData:', error);\n            $content.html(`\r\n                <div style=\"padding: 20px; text-align: center;\">\r\n                    <p style=\"color: #ff6b6b; margin-bottom: 10px;\">\r\n                        <i class=\"fa-solid fa-exclamation-triangle\"></i> 数据加载失败\r\n                    </p>\r\n                    <p style=\"color: #666; font-size: 14px;\">\r\n                        请检查开发者控制台获取详细信息，或尝试刷新页面。\r\n                    </p>\r\n                    <button class=\"wio-modal-btn\" onclick=\"$('#${REFRESH_BTN_ID}').click()\"\r\n                            style=\"margin-top: 15px; padding: 8px 16px;\">\r\n                        <i class=\"fa-solid fa-refresh\"></i> 重试\r\n                    </button>\r\n                </div>\r\n            `);\n            throw error;\n        }\n    });\n    // --- 角色正则和世界书更新函数 ---\n    function updateCharacterRegexes(allUIRegexes, charData) {\n        const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];\n        let cardRegexes = [];\n        if (charData && TavernAPI && TavernAPI.Character) {\n            try {\n                const character = new TavernAPI.Character(charData);\n                cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                    id: r.id || `card-${Date.now()}-${i}`,\n                    script_name: r.scriptName || '未命名卡内正则',\n                    find_regex: r.findRegex,\n                    replace_string: r.replaceString,\n                    enabled: !r.disabled,\n                    scope: 'character',\n                    source: 'card',\n                }));\n            }\n            catch (e) {\n                console.warn('无法解析角色卡正则脚本:', e);\n            }\n        }\n        const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n        const uniqueCardRegexes = cardRegexes.filter((r) => {\n            const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n            return !uiRegexIdentifiers.has(identifier);\n        });\n        appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];\n    }\n    function updateCharacterLorebooks(charBooks) {\n        const characterBookNames = [];\n        if (charBooks) {\n            if (charBooks.primary)\n                characterBookNames.push(charBooks.primary);\n            if (charBooks.additional)\n                characterBookNames.push(...charBooks.additional);\n        }\n        appState.lorebooks.character = [...new Set(characterBookNames)];\n    }\n    /**\n     * 统一的数据筛选与处理函数\n     * @param books - 要处理的世界书数组 (例如 appState.allLorebooks)\n     * @param searchTerm - 当前搜索词\n     * @returns 过滤和处理后的数据，包含是否展开的标志\n     */\n    const getFilteredLorebookData = (books, searchTerm) => {\n        if (!searchTerm) {\n            return books.map(book => ({\n                book,\n                entries: [...safeGetLorebookEntries(book.name)],\n                shouldExpand: false, // 无搜索词时不自动展开\n            }));\n        }\n        const filteredData = [];\n        books.forEach(book => {\n            const entries = [...safeGetLorebookEntries(book.name)];\n            const bookNameMatches = appState.searchFilters.bookName && isMatch(book.name, searchTerm);\n            const matchingEntries = entries.filter(entry => (appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm)) ||\n                (appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm)) ||\n                (appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm)));\n            if (bookNameMatches || matchingEntries.length > 0) {\n                filteredData.push({\n                    book,\n                    // 如果书名匹配，显示所有条目；否则只显示匹配的条目\n                    entries: bookNameMatches ? entries : matchingEntries,\n                    // 核心逻辑：如果书名匹配或有条目匹配，则应展开\n                    shouldExpand: true,\n                });\n            }\n        });\n        return filteredData;\n    };\n    // --- 渲染函数 ---\n    const renderContent = () => {\n        // 根据全局状态同步所有多选按钮的UI\n        const $multiSelectToggles = $(`.wio-multi-select-toggle`, parentWin.document);\n        if (appState.multiSelectMode) {\n            $multiSelectToggles.addClass('active').html('<i class=\"fa-solid fa-times\"></i> 退出多选');\n        }\n        else {\n            $multiSelectToggles.removeClass('active').html('<i class=\"fa-solid fa-check-square\"></i> 多选模式');\n        }\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val() || '';\n        appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');\n        appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');\n        appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');\n        appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.empty();\n        $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);\n        const isLoreTab = appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';\n        $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);\n        const isAnyEditing = appState.multiSelectMode || $('.wio-book-group.editing-entries', parentWin.document).length > 0;\n        $(`#wio-multi-select-controls`, parentWin.document).toggle(isAnyEditing);\n        updateSelectionCount();\n        switch (appState.activeTab) {\n            case 'global-lore':\n                renderGlobalLorebookView(searchTerm, $content);\n                break;\n            case 'char-lore':\n                renderCharacterLorebookView(searchTerm, $content);\n                break;\n            case 'chat-lore':\n                renderChatLorebookView(searchTerm, $content);\n                break;\n            case 'global-regex':\n                renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');\n                break;\n            case 'char-regex':\n                renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');\n                break;\n        }\n    };\n    // --- 选择和批量操作函数 ---\n    const getAllVisibleItems = () => {\n        const visibleItems = [];\n        const activeTab = appState.activeTab;\n        if (activeTab === 'global-lore') {\n            appState.allLorebooks.forEach(book => {\n                visibleItems.push({ type: 'book', id: book.name, enabled: book.enabled });\n                [...safeGetLorebookEntries(book.name)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName: book.name, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'char-lore') {\n            appState.lorebooks.character.forEach(bookName => {\n                [...safeGetLorebookEntries(bookName)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'global-regex') {\n            appState.regexes.global.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        else if (activeTab === 'char-regex') {\n            appState.regexes.character.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        return visibleItems;\n    };\n    const renderGlobalLorebookView = (searchTerm, $container) => {\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        // 调用新的辅助函数来处理数据\n        const filteredBookData = getFilteredLorebookData(books, searchTerm);\n        if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书。</p>`);\n        }\n        else if (appState.allLorebooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">还没有世界书，点击上方\"+\"创建一个吧。</p>`);\n        }\n        filteredBookData.forEach(data => {\n            if (data && data.book) {\n                $container.append(createGlobalLorebookElement(data.book, data.entries, searchTerm, data.shouldExpand));\n            }\n        });\n    };\n    const renderCharacterLorebookView = (searchTerm, $container) => {\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter) {\n            $container.html(`<p class=\"wio-info-text\">请先加载一个角色以管理角色世界书。</p>`);\n            return;\n        }\n        if (linkedBooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">当前角色没有绑定的世界书。点击同步按钮刷新。</p>`);\n            return;\n        }\n        // 转换数据格式以复用 getFilteredLorebookData\n        const booksForFiltering = linkedBooks.map(name => ({ name }));\n        const filteredBookData = getFilteredLorebookData(booksForFiltering, searchTerm);\n        if (filteredBookData.length === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书或条目。</p>`);\n            return;\n        }\n        filteredBookData.forEach(data => {\n            const { book, entries, shouldExpand } = data;\n            const bookName = book.name;\n            const $bookContainer = $(`\r\n        <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n          <div class=\"wio-book-group-header\">\r\n            <span>${highlightText(bookName, searchTerm)}</span>\r\n            <div class=\"wio-item-controls\">\r\n              <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n              <button class=\"wio-action-btn-icon wio-edit-entries-btn\" title=\"多选条目\"><i class=\"fa-solid fa-list-check\"></i></button>\r\n              <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-collapsible-content\" style=\"${shouldExpand ? 'display: block;' : ''}\">\r\n            <div class=\"wio-entry-list-wrapper\"></div>\r\n          </div>\r\n        </div>\r\n      `);\n            const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n            const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button><button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-shield-halved\"></i> 全开防递</button><button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-check-double\"></i> 修复关键词</button></div>`);\n            $listWrapper.append($entryActions);\n            const sortedEntries = [...entries].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            if (sortedEntries.length === 0 && searchTerm) {\n                $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n            }\n            else {\n                sortedEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n            }\n            $container.append($bookContainer);\n        });\n    };\n    const renderChatLorebookView = (searchTerm, $container) => {\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat) {\n            $container.html(`<p class=\"wio-info-text\">请先开始一个聊天以管理聊天世界书。</p>`);\n            return;\n        }\n        if (!bookName) {\n            $container.html(`\r\n        <div class=\"wio-info-section\">\r\n          <p class=\"wio-info-text\">当前聊天没有绑定世界书。</p>\r\n          <button id=\"wio-create-chat-lore-btn\" class=\"wio-btn wio-btn-primary\">\r\n            <i class=\"fa-solid fa-plus\"></i> 创建聊天世界书\r\n          </button>\r\n        </div>\r\n      `);\n            return;\n        }\n        // 复用 getFilteredLorebookData\n        const booksForFiltering = [{ name: bookName }];\n        const filteredBookData = getFilteredLorebookData(booksForFiltering, searchTerm);\n        if (filteredBookData.length === 0) {\n            // 即使没有匹配项，如果存在聊天世界书，也应该显示其框架\n            filteredBookData.push({\n                book: { name: bookName },\n                entries: [],\n                shouldExpand: false,\n            });\n        }\n        const { book, entries, shouldExpand } = filteredBookData[0];\n        const $bookContainer = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n        <div class=\"wio-book-group-header\">\r\n          <span>${highlightText(bookName, searchTerm)} (聊天世界书)</span>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-edit-entries-btn\" title=\"多选条目\"><i class=\"fa-solid fa-list-check\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-unlink-chat-lore-btn\" title=\"解除绑定\"><i class=\"fa-solid fa-unlink\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-collapsible-content\" style=\"${shouldExpand ? 'display: block;' : ''}\">\r\n           <div class=\"wio-entry-list-wrapper\"></div>\r\n        </div>\r\n      </div>\r\n    `);\n        const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n        const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button></div>`);\n        $listWrapper.append($entryActions);\n        const sortedEntries = [...entries].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        if (sortedEntries.length === 0 && searchTerm) {\n            $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n        }\n        else {\n            sortedEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n        }\n        $container.empty().append($bookContainer);\n    };\n    const renderRegexView = (regexes, searchTerm, $container, title) => {\n        if (regexes.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">没有找到${title}。</p>`);\n            return;\n        }\n        // 按启用状态和名称排序\n        const sortedRegexes = [...regexes].sort((a, b) => Number(b.enabled) - Number(a.enabled) || (a.script_name || '').localeCompare(b.script_name || ''));\n        // 过滤匹配项\n        let filteredRegexes = sortedRegexes;\n        if (searchTerm) {\n            filteredRegexes = sortedRegexes.filter(regex => {\n                const name = regex.script_name || '';\n                const findRegex = regex.find_regex || '';\n                const replaceString = regex.replace_string || '';\n                return (name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    findRegex.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    replaceString.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n        }\n        if (filteredRegexes.length === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的${title}。</p>`);\n            return;\n        }\n        // 添加操作按钮区域\n        const $actions = $(`\r\n      <div class=\"wio-regex-actions\">\r\n        <button class=\"wio-action-btn wio-create-regex-btn\" data-scope=\"${title === '全局正则' ? 'global' : 'character'}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-import-regex-btn\">\r\n          <i class=\"fa-solid fa-upload\"></i> 导入正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-export-regex-btn\">\r\n          <i class=\"fa-solid fa-download\"></i> 导出正则\r\n        </button>\r\n        <button class=\"wio-btn wio-multi-select-toggle\">\r\n          <i class=\"fa-solid fa-check-square\"></i> 多选模式\r\n        </button>\r\n      </div>\r\n    `);\n        $container.append($actions);\n        // 渲染正则列表\n        const $regexList = $('<div class=\"wio-regex-list\"></div>');\n        filteredRegexes.forEach((regex, index) => {\n            const $element = createItemElement(regex, 'regex', '', searchTerm);\n            // 添加序号指示器\n            $element.find('.wio-item-name').prepend(`<span class=\"wio-order-indicator\">#${index + 1}</span> `);\n            $regexList.append($element);\n        });\n        $container.append($regexList);\n        // 初始化拖拽排序（仅对非搜索状态的完整列表）\n        if (!searchTerm && parentWin.Sortable) {\n            const listEl = $regexList[0];\n            if (listEl) {\n                new parentWin.Sortable(listEl, {\n                    animation: 150,\n                    handle: '.wio-drag-handle',\n                    ghostClass: 'sortable-ghost',\n                    chosenClass: 'sortable-chosen',\n                    onEnd: (evt) => handleRegexDragEnd(evt, title === '全局正则' ? 'global' : 'character'),\n                });\n            }\n        }\n    };\n    // --- 核心UI元素创建函数 ---\n    const createItemElement = (item, type, bookName = '', searchTerm = '') => {\n        const isLore = type === 'lore';\n        const id = isLore ? item.uid : item.id;\n        const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';\n        const fromCard = item.source === 'card';\n        let controlsHtml = '';\n        if (isLore) {\n            // 所有世界书条目都有完整的操作按钮\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n        <button class=\"wio-action-btn-icon wio-delete-entry-btn\" title=\"删除条目\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n      `;\n        }\n        else if (fromCard) {\n            // 来自卡片的正则只有开关\n            controlsHtml =\n                '<button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>';\n        }\n        else {\n            // UI中的正则有重命名和开关\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n      `;\n        }\n        const dragHandleHtml = !fromCard && !isLore\n            ? '<span class=\"wio-drag-handle\" title=\"拖拽排序\"><i class=\"fa-solid fa-grip-vertical\"></i></span>'\n            : '';\n        // 应用高亮到条目名称\n        const highlightedName = highlightText(name, searchTerm);\n        const $element = $(`<div class=\"wio-item-container ${fromCard ? 'from-card' : ''}\" data-type=\"${type}\" data-id=\"${id}\" ${isLore ? `data-book-name=\"${escapeHtml(bookName)}\"` : ''}><div class=\"wio-item-header\" title=\"${fromCard ? '此条目来自角色卡，部分操作受限' : appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/编辑'}\">${dragHandleHtml}<span class=\"wio-item-name\">${highlightedName}</span><div class=\"wio-item-controls\">${controlsHtml}</div></div><div class=\"wio-collapsible-content\"></div></div>`);\n        // 保存搜索词以便在内容展开时使用\n        $element.data('searchTerm', searchTerm);\n        $element.toggleClass('enabled', item.enabled);\n        if (appState.multiSelectMode) {\n            const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;\n            $element.toggleClass('selected', appState.selectedItems.has(itemKey));\n        }\n        return $element;\n    };\n    const createGlobalLorebookElement = (book, entriesToShow, searchTerm, isExpanded) => {\n        const usedByChars = appState.lorebookUsage.get(book.name) || [];\n        const usedByHtml = usedByChars.length > 0\n            ? `<div class=\"wio-used-by-chars\">使用者: ${usedByChars.map(char => `<span>${escapeHtml(char)}</span>`).join(', ')}</div>`\n            : '';\n        const $element = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(book.name)}\">\r\n        <div class=\"wio-global-book-header\" title=\"${appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/折叠'}\">\r\n          <div class=\"wio-book-info\">\r\n            <span class=\"wio-book-name\">${highlightText(book.name, searchTerm)}</span>\r\n            <span class=\"wio-book-status ${book.enabled ? 'enabled' : 'disabled'}\">${book.enabled ? '已启用' : '已禁用'}</span>\r\n            ${usedByHtml}\r\n          </div>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-edit-entries-btn\" title=\"多选条目\"><i class=\"fa-solid fa-list-check\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-collapsible-content\" style=\"${isExpanded ? 'display: block;' : ''}\"></div>\r\n      </div>\r\n    `);\n        const $content = $element.find('.wio-collapsible-content');\n        // 添加条目操作按钮\n        const $entryActions = $(`\r\n      <div class=\"wio-entry-actions\">\r\n        <button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建条目\r\n        </button>\r\n        <button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-shield-halved\"></i> 全开防递\r\n        </button>\r\n        <button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-check-double\"></i> 修复关键词\r\n        </button>\r\n      </div>\r\n    `);\n        $content.append($entryActions);\n        const sortedEntries = [...entriesToShow].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        if (sortedEntries.length > 0) {\n            const $listWrapper = $('<div class=\"wio-entry-list-wrapper\"></div>');\n            sortedEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', book.name, searchTerm)));\n            $content.append($listWrapper);\n        }\n        else if (searchTerm) {\n            $content.append(`<div class=\"wio-info-text-small\">无匹配项</div>`);\n        }\n        return $element;\n    };\n    // --- 替换功能实现 ---\n    const handleReplace = errorCatched(async () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val();\n        const replaceTerm = $('#wio-replace-input', parentWin.document).val();\n        // 检查搜索词是否为空\n        if (!searchTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入搜索词。' });\n            return;\n        }\n        // 检查替换词是否为空\n        if (!replaceTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入替换词。' });\n            return;\n        }\n        // 获取当前视图的匹配项\n        let matches = [];\n        switch (appState.activeTab) {\n            case 'global-lore':\n                matches = getGlobalLorebookMatches(searchTerm);\n                break;\n            case 'char-lore':\n                matches = getCharacterLorebookMatches(searchTerm);\n                break;\n            case 'chat-lore':\n                matches = getChatLorebookMatches(searchTerm);\n                break;\n            default:\n                await showModal({ type: 'alert', title: '替换失败', text: '替换功能仅支持世界书视图。' });\n                return;\n        }\n        // 如果没有匹配项，提示用户\n        if (matches.length === 0) {\n            await showModal({ type: 'alert', title: '替换失败', text: '未找到匹配的条目。' });\n            return;\n        }\n        // 显示确认对话框\n        const confirmResult = await showModal({\n            type: 'confirm',\n            title: '确认替换',\n            text: `找到 ${matches.length} 个匹配项。\\n\\n确定要将 \"${searchTerm}\" 替换为 \"${replaceTerm}\" 吗？\\n\\n注意：此操作仅替换条目的关键词、内容和条目名称，不会替换世界书本身的名称。\\n此操作不可撤销，请谨慎操作。`,\n        });\n        // 如果用户确认替换，则执行替换\n        if (confirmResult) {\n            const progressToast = showProgressToast('正在执行替换...');\n            try {\n                await performReplace(matches, searchTerm, replaceTerm);\n                progressToast.remove();\n                showSuccessTick('替换完成');\n                // 核心实现：将被替换文字放入搜索框，清空替换框\n                $(`#${SEARCH_INPUT_ID}`, parentWin.document).val(replaceTerm);\n                $('#wio-replace-input', parentWin.document).val('');\n                // 重新初始化以获取最新数据并刷新视图\n                await loadAllData();\n            }\n            catch (error) {\n                progressToast.remove();\n                console.error('[WorldInfoOptimizer] Replace error:', error);\n                await showModal({\n                    type: 'alert',\n                    title: '替换失败',\n                    text: '替换过程中发生错误，请检查开发者控制台获取详细信息。',\n                });\n            }\n        }\n    });\n    // 执行替换操作的函数\n    const performReplace = async (matches, searchTerm, replaceTerm) => {\n        // 创建一个映射来跟踪每个世界书的更改\n        const bookUpdates = new Map();\n        // 遍历所有匹配项\n        for (const match of matches) {\n            const { bookName, entry } = match;\n            let updated = false;\n            // 如果还没有为这个世界书创建更新数组，则创建一个\n            if (!bookUpdates.has(bookName)) {\n                bookUpdates.set(bookName, []);\n            }\n            // 创建条目的深拷贝以进行修改\n            const updatedEntry = JSON.parse(JSON.stringify(entry));\n            // 替换关键词\n            if (updatedEntry.keys && Array.isArray(updatedEntry.keys)) {\n                const newKeys = updatedEntry.keys.map((key) => key.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm));\n                // 检查是否有实际更改\n                if (JSON.stringify(updatedEntry.keys) !== JSON.stringify(newKeys)) {\n                    updatedEntry.keys = newKeys;\n                    updated = true;\n                }\n            }\n            // 替换条目内容\n            if (updatedEntry.content) {\n                const newContent = updatedEntry.content.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.content !== newContent) {\n                    updatedEntry.content = newContent;\n                    updated = true;\n                }\n            }\n            // 替换条目名称（comment）\n            if (updatedEntry.comment) {\n                const newComment = updatedEntry.comment.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.comment !== newComment) {\n                    updatedEntry.comment = newComment;\n                    updated = true;\n                }\n            }\n            // 如果有更改，则将更新后的条目添加到更新数组中\n            if (updated) {\n                bookUpdates.get(bookName).push(updatedEntry);\n            }\n        }\n        // 应用所有更改\n        for (const [bookName, entriesToUpdate] of bookUpdates.entries()) {\n            if (entriesToUpdate.length > 0) {\n                // 调用TavernAPI来更新条目\n                const result = await TavernAPI.setLorebookEntries(bookName, entriesToUpdate);\n                if (result && result.entries) {\n                    // 更新本地状态\n                    safeSetLorebookEntries(bookName, result.entries);\n                }\n            }\n        }\n        // 等待一段时间以确保所有操作完成\n        await new Promise(resolve => setTimeout(resolve, 100));\n    };\n    // 获取匹配项的函数\n    const getGlobalLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                entries.forEach(entry => {\n                    matches.push({ bookName: book.name, entry });\n                });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && isMatch(book.name, searchTerm);\n                entries.forEach(entry => {\n                    const entryNameMatches = appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm);\n                    const keywordsMatch = appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm);\n                    const contentMatch = appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm);\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName: book.name, entry });\n                    }\n                });\n            });\n        }\n        return matches;\n    };\n    const getCharacterLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter || linkedBooks.length === 0) {\n            return matches;\n        }\n        linkedBooks.forEach(bookName => {\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            if (!searchTerm) {\n                // 如果没有搜索词，返回所有条目\n                entries.forEach(entry => {\n                    matches.push({ bookName, entry });\n                });\n            }\n            else {\n                // 根据搜索词和过滤器获取匹配项\n                entries.forEach(entry => {\n                    const bookNameMatches = appState.searchFilters.bookName && isMatch(bookName, searchTerm);\n                    const entryNameMatches = appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm);\n                    const keywordsMatch = appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm);\n                    const contentMatch = appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm);\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName, entry });\n                    }\n                });\n            }\n        });\n        return matches;\n    };\n    const getChatLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat || !bookName) {\n            return matches;\n        }\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            entries.forEach(entry => {\n                matches.push({ bookName, entry });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            entries.forEach(entry => {\n                const entryNameMatches = appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm);\n                const keywordsMatch = appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm);\n                const contentMatch = appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm);\n                // 如果条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                if (entryNameMatches || keywordsMatch || contentMatch) {\n                    matches.push({ bookName, entry });\n                }\n            });\n        }\n        return matches;\n    };\n    // --- SortableJS 加载和拖拽排序功能 ---\n    const loadSortableJS = (callback) => {\n        if (parentWin.Sortable) {\n            callback();\n            return;\n        }\n        const script = parentWin.document.createElement('script');\n        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.2/Sortable.min.js';\n        script.onload = () => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded successfully.');\n            callback();\n        };\n        script.onerror = () => {\n            console.error('[WorldInfoOptimizer] Failed to load SortableJS.');\n            showModal({ type: 'alert', title: '错误', text: '无法加载拖拽排序库，请检查网络连接或浏览器控制台。' });\n        };\n        parentWin.document.head.appendChild(script);\n    };\n    // 防抖函数\n    const debounce = (func, delay) => {\n        let timeout;\n        return (...args) => {\n            clearTimeout(timeout);\n            timeout = setTimeout(() => func(...args), delay);\n        };\n    };\n    // 防抖保存正则顺序\n    const debouncedSaveRegexOrder = debounce(errorCatched(async () => {\n        const allRegexes = [...appState.regexes.global, ...appState.regexes.character];\n        await TavernAPI.replaceRegexes(allRegexes.filter(r => r.source !== 'card'));\n        await TavernAPI.saveSettings();\n        showSuccessTick('正则顺序已保存');\n    }), 800);\n    // 处理正则拖拽结束事件\n    const handleRegexDragEnd = errorCatched(async (evt, scope) => {\n        const { oldIndex, newIndex } = evt;\n        if (oldIndex === newIndex)\n            return;\n        const targetList = appState.regexes[scope];\n        const [movedItem] = targetList.splice(oldIndex, 1);\n        targetList.splice(newIndex, 0, movedItem);\n        // 乐观更新UI：重新渲染序号\n        renderContent();\n        // 防抖保存\n        debouncedSaveRegexOrder();\n    });\n    // --- 主程序逻辑 ---\n    function main(jquery, tavernHelper) {\n        $ = jquery;\n        TavernHelper = tavernHelper;\n        console.log('[WorldInfoOptimizer] Initializing main application...');\n        // 初始化 TavernAPI\n        initializeTavernAPI();\n        // --- 探查代码 ---\n        console.log('[WorldInfoOptimizer] Probing for event listeners...');\n        console.log('[WorldInfoOptimizer] TavernHelper object:', TavernHelper);\n        console.log('[WorldInfoOptimizer] SillyTavern object:', parentWin.SillyTavern);\n        // --- 探查代码结束 ---\n        // 加载 SortableJS 然后初始化 UI\n        loadSortableJS(() => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded, creating UI elements...');\n            // 创建主面板\n            createMainPanel();\n            // 创建扩展菜单按钮\n            createExtensionButton();\n            // 绑定事件处理器\n            bindEventHandlers();\n            // 加载初始数据\n            loadAllData();\n            console.log('[WorldInfoOptimizer] Main application initialized successfully.');\n        });\n    }\n    // --- UI 创建函数 ---\n    const createMainPanel = () => {\n        const parentDoc = parentWin.document;\n        // 检查面板是否已存在\n        if ($(`#${PANEL_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');\n            return;\n        }\n        const panelHtml = `\r\n            <div id=\"${PANEL_ID}\" class=\"wio-panel\" style=\"display: none;\">\r\n                <div class=\"wio-panel-header\">\r\n                    <h3 class=\"wio-panel-title\">\r\n                        <i class=\"fa-solid fa-book\"></i> 世界书优化器\r\n                    </h3>\r\n                    <div class=\"wio-panel-controls\">\r\n                        <button id=\"${REFRESH_BTN_ID}\" class=\"wio-btn wio-btn-icon\" title=\"刷新数据\">\r\n                            <i class=\"fa-solid fa-sync-alt\"></i>\r\n                        </button>\r\n                        <button class=\"wio-btn wio-btn-icon wio-panel-close\" title=\"关闭\">\r\n                            <i class=\"fa-solid fa-times\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"wio-panel-body\">\r\n                    <div class=\"wio-tabs\">\r\n                        <button class=\"wio-tab-btn active\" data-tab=\"global-lore\">全局世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-lore\">角色世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"chat-lore\">聊天世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"global-regex\">全局正则</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-regex\">角色正则</button>\r\n                    </div>\r\n                    <div class=\"wio-search-section\">\r\n                        <div class=\"wio-search-bar\">\r\n                            <input type=\"text\" id=\"${SEARCH_INPUT_ID}\" placeholder=\"搜索世界书、条目、关键词...\" class=\"wio-search-input\">\r\n                            <input type=\"text\" id=\"wio-replace-input\" placeholder=\"替换为...\" class=\"wio-search-input\">\r\n                            <button id=\"wio-replace-btn\" class=\"wio-btn wio-search-btn\" title=\"替换\">\r\n                                <i class=\"fa-solid fa-exchange-alt\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div id=\"wio-search-filters-container\" class=\"wio-search-filters\">\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-book-name\" checked> 书名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-entry-name\" checked> 条目名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-keywords\" checked> 关键词</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-content\" checked> 内容</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"wio-toolbar\">\r\n                        <button id=\"${CREATE_LOREBOOK_BTN_ID}\" class=\"wio-btn wio-btn-primary\">\r\n                            <i class=\"fa-solid fa-plus\"></i> 新建世界书\r\n                        </button>\r\n                        <button id=\"${COLLAPSE_ALL_BTN_ID}\" class=\"wio-btn\">\r\n                            <i class=\"fa-solid fa-compress-alt\"></i> 全部折叠\r\n                        </button>\r\n                        <button class=\"wio-btn wio-multi-select-toggle\">\r\n                            <i class=\"fa-solid fa-check-square\"></i> 多选模式\r\n                        </button>\r\n                        <div id=\"wio-multi-select-controls\" class=\"wio-multi-select-controls\" style=\"display: none;\">\r\n                            <div class=\"wio-multi-select-actions\">\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-all-btn\">全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-none-btn\">取消全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-invert-btn\">反选</button>\r\n                                <button class=\"wio-multi-select-action-btn enable\" id=\"wio-batch-enable-btn\">批量启用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-disable-btn\">批量禁用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-delete-btn\">批量删除</button>\r\n                                <span class=\"wio-selection-count\" id=\"wio-selection-count\">已选择: 0</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div id=\"${PANEL_ID}-content\" class=\"wio-content\">\r\n                        <p class=\"wio-info-text\">正在初始化...</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        `;\n        $('body', parentDoc).append(panelHtml);\n        // 添加基础样式\n        addBasicStyles();\n    };\n    const createExtensionButton = () => {\n        const parentDoc = parentWin.document;\n        // 检查按钮是否已存在\n        if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');\n            return;\n        }\n        const buttonHtml = `\r\n            <div id=\"${BUTTON_ID}\" class=\"list-group-item flex-container flexGap5 interactable\" title=\"${BUTTON_TOOLTIP}\">\r\n                <div class=\"fa-solid fa-book extensionsMenuExtensionButton\" title=\"${BUTTON_TOOLTIP}\"></div>\r\n                <span>${BUTTON_TEXT_IN_MENU}</span>\r\n            </div>\r\n        `;\n        const $extensionsMenu = $('#extensionsMenu', parentDoc);\n        if ($extensionsMenu.length > 0) {\n            $extensionsMenu.append(buttonHtml);\n            console.log(`[WorldInfoOptimizer] Button #${BUTTON_ID} appended to #extensionsMenu.`);\n        }\n        else {\n            console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');\n        }\n    };\n    const addBasicStyles = () => {\n        const parentDoc = parentWin.document;\n        // 检查样式是否已添加\n        if ($('#wio-basic-styles', parentDoc).length > 0) {\n            return;\n        }\n        const basicStyles = `\r\n            <style id=\"wio-basic-styles\">\r\n                /* 面板基础样式 - 响应式改进 */\r\n                #world-info-optimizer-panel {\r\n                    position: fixed;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 95vw;\r\n                    max-width: 1200px;\r\n                    height: 85vh;\r\n                    max-height: 800px;\r\n                    background-color: #1a1a1a;\r\n                    border: 2px solid #444;\r\n                    border-radius: 8px;\r\n                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);\r\n                    z-index: 10000;\r\n                    display: none;\r\n                    flex-direction: column;\r\n                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n                    color: #fff;\r\n                }\r\n\r\n                /* 移动端适配 - 修复顶部溢出和视窗单位问题 */\r\n                @media (max-width: 768px) {\r\n                    #world-info-optimizer-panel {\r\n                        /* 使用dvh单位替代vh，考虑地址栏空间 */\r\n                        width: 98vw;\r\n                        height: 95dvh;\r\n                        max-height: none;\r\n                        border-radius: 0;\r\n                        /* 从顶部开始，避免被推出屏幕 */\r\n                        top: 0;\r\n                        left: 0;\r\n                        transform: none;\r\n                        margin: 0;\r\n                        /* 考虑安全区域 */\r\n                        padding-top: env(safe-area-inset-top, 0);\r\n                        padding-bottom: env(safe-area-inset-bottom, 0);\r\n                        padding-left: env(safe-area-inset-left, 0);\r\n                        padding-right: env(safe-area-inset-right, 0);\r\n                    }\r\n                    \r\n                    .wio-panel-header {\r\n                        padding: 10px 15px !important;\r\n                        /* 确保头部不被系统状态栏遮挡 */\r\n                        padding-top: calc(10px + env(safe-area-inset-top, 0));\r\n                    }\r\n                    \r\n                    .wio-toolbar {\r\n                        padding: 10px 15px !important;\r\n                    }\r\n                }\r\n\r\n                @media (max-width: 480px) {\r\n                    #world-info-optimizer-panel {\r\n                        width: 100vw;\r\n                        height: 100dvh;\r\n                        /* 完全填充，无margin */\r\n                        top: 0;\r\n                        left: 0;\r\n                        transform: none;\r\n                        margin: 0;\r\n                        /* 全面考虑安全区域 */\r\n                        padding-top: env(safe-area-inset-top, 0);\r\n                        padding-bottom: env(safe-area-inset-bottom, 0);\r\n                        padding-left: env(safe-area-inset-left, 0);\r\n                        padding-right: env(safe-area-inset-right, 0);\r\n                    }\r\n                }\r\n\r\n                /* 中等屏幕适配：768px-1200px */\r\n                @media (max-width: 1200px) and (min-width: 769px) {\r\n                    #world-info-optimizer-panel {\r\n                        width: min(95vw, 1000px);\r\n                        height: min(88vh, 750px);\r\n                        max-width: 1000px;\r\n                        max-height: 750px;\r\n                        /* 新增：修复中等屏幕下的垂直居中溢出问题 */\r\n                        top: 20px;\r\n                        transform: translate(-50%, 0);\r\n                    }\r\n                    \r\n                    .wio-toolbar {\r\n                        gap: 8px;\r\n                        padding: 12px 18px;\r\n                    }\r\n                    \r\n                    .wio-btn {\r\n                        padding: 6px 12px;\r\n                        font-size: 13px;\r\n                        white-space: nowrap;\r\n                        flex: 0 0 auto; /* 防止拉伸 */\r\n                    }\r\n                }\r\n\r\n                /* 平板适配：768px-1024px */\r\n                @media (max-width: 1024px) and (min-width: 769px) {\r\n                    #world-info-optimizer-panel {\r\n                        width: 92vw;\r\n                        height: 85vh;\r\n                        max-width: 900px;\r\n                        max-height: 700px;\r\n                    }\r\n                    \r\n                    .wio-toolbar {\r\n                        gap: 6px;\r\n                        padding: 10px 15px;\r\n                    }\r\n\r\n                    .wio-btn {\r\n                        padding: 5px 10px;\r\n                        font-size: 12px;\r\n                        flex: 0 0 auto;\r\n                    }\r\n                    \r\n                    .wio-content {\r\n                        padding: 15px;\r\n                    }\r\n                }\r\n\r\n                /* 按钮基础类系统 */\r\n                .wio-btn-base {\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    transition: all 0.2s ease;\r\n                    font-family: inherit;\r\n                    display: inline-flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    text-decoration: none;\r\n                    outline: none;\r\n                }\r\n\r\n                /* 按钮尺寸修饰符 */\r\n                .wio-btn-sm { padding: 4px 8px; font-size: 12px; }\r\n                .wio-btn-md { padding: 8px 16px; font-size: 14px; }\r\n                .wio-btn-lg { padding: 12px 24px; font-size: 16px; }\r\n\r\n                /* 图标按钮尺寸 */\r\n                .wio-btn-icon-sm { padding: 4px; width: 24px; height: 24px; }\r\n                .wio-btn-icon-md { padding: 8px; width: 36px; height: 36px; }\r\n\r\n                /* 按钮颜色主题 */\r\n                .wio-btn-primary { background: #007bff; color: #fff; }\r\n                .wio-btn-primary:hover { background: #0056b3; }\r\n\r\n                .wio-btn-secondary { background: #6c757d; color: #fff; }\r\n                .wio-btn-secondary:hover { background: #5a6268; }\r\n\r\n                .wio-btn-success { background: #28a745; color: #fff; }\r\n                .wio-btn-success:hover { background: #218838; }\r\n\r\n                .wio-btn-danger { background: #dc3545; color: #fff; }\r\n                .wio-btn-danger:hover { background: #c82333; }\r\n\r\n                /* 统一hover效果 */\r\n                .wio-btn-base:hover {\r\n                    transform: translateY(-1px);\r\n                    filter: brightness(1.05);\r\n                }\r\n\r\n                /* 覆盖特定按钮的hover效果 */\r\n                .wio-btn-primary:hover,\r\n                .wio-btn-secondary:hover,\r\n                .wio-btn-success:hover,\r\n                .wio-btn-danger:hover {\r\n                    filter: none;\r\n                    transform: translateY(-1px);\r\n                }\r\n                .wio-panel-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                    border-radius: 8px 8px 0 0;\r\n                }\r\n                .wio-panel-title {\r\n                    margin: 0;\r\n                    font-size: 18px;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-controls {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                }\r\n                .wio-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-btn:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-btn-primary {\r\n                    background: #007bff;\r\n                }\r\n                .wio-btn-primary:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-btn-icon {\r\n                    padding: 8px;\r\n                    width: 36px;\r\n                    height: 36px;\r\n                }\r\n                .wio-panel-body {\r\n                    flex: 1;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    overflow: hidden;\r\n                }\r\n                .wio-tabs {\r\n                    display: flex;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                }\r\n                .wio-tab-btn {\r\n                    padding: 12px 20px;\r\n                    border: none;\r\n                    background: transparent;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                    border-bottom: 2px solid transparent;\r\n                    transition: all 0.2s;\r\n                }\r\n                .wio-tab-btn:hover {\r\n                    background: #444;\r\n                    color: #fff;\r\n                }\r\n                .wio-tab-btn.active {\r\n                    color: #fff;\r\n                    border-bottom-color: #007bff;\r\n                    background: #444;\r\n                }\r\n                .wio-search-section {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-search-bar {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    margin-bottom: 10px;\r\n                }\r\n                .wio-search-input {\r\n                    flex: 1;\r\n                    padding: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-search-filters {\r\n                    display: flex;\r\n                    gap: 15px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-search-filters label {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 5px;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                }\r\n                .wio-toolbar {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                    display: flex;\r\n                    flex-wrap: wrap;\r\n                    gap: 8px;\r\n                    align-items: center;\r\n                }\r\n                .wio-content {\r\n                    flex: 1;\r\n                    padding: 20px;\r\n                    overflow-y: auto;\r\n                    background: #1a1a1a;\r\n                }\r\n                .wio-info-text {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 40px 0;\r\n                }\r\n                .wio-book-item {\r\n                    margin-bottom: 20px;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #444;\r\n                }\r\n                .wio-highlight {\r\n                    background: #ffeb3b;\r\n                    color: #000;\r\n                    padding: 1px 2px;\r\n                    border-radius: 2px;\r\n                }\r\n                .wio-item-container {\r\n                    margin-bottom: 10px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                }\r\n                .wio-item-container.enabled {\r\n                    border-left: 3px solid #28a745;\r\n                }\r\n                .wio-item-container.selected {\r\n                    background: #2a4a6b;\r\n                    border-color: #007bff;\r\n                }\r\n                .wio-item-header {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    padding: 10px 15px;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-item-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-item-name {\r\n                    flex: 1;\r\n                    margin-left: 10px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-item-controls {\r\n                    display: flex;\r\n                    gap: 5px;\r\n                }\r\n                .wio-action-btn-icon {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn-icon:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-toggle-btn {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #dc3545;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-toggle-btn:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-book-group {\r\n                    margin-bottom: 20px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 6px;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-book-group-header, .wio-global-book-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px 6px 0 0;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-book-group-header:hover, .wio-global-book-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-book-name {\r\n                    font-size: 16px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status {\r\n                    margin-left: 10px;\r\n                    padding: 2px 8px;\r\n                    border-radius: 12px;\r\n                    font-size: 12px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-book-status.enabled {\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status.disabled {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-entry-actions, .wio-regex-actions {\r\n                    padding: 15px;\r\n                    border-bottom: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-action-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn:hover {\r\n                    background: #0056b3;\r\n                }\r\n\r\n                .wio-multi-select-actions {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                    align-items: center;\r\n                }\r\n                .wio-multi-select-action-btn {\r\n                    padding: 6px 12px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-multi-select-action-btn:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-multi-select-action-btn.enable {\r\n                    background: #28a745;\r\n                }\r\n                .wio-multi-select-action-btn.enable:hover {\r\n                    background: #218838;\r\n                }\r\n                .wio-multi-select-action-btn.disable {\r\n                    background: #dc3545;\r\n                }\r\n                .wio-multi-select-action-btn.disable:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-selection-count {\r\n                    margin-left: auto;\r\n                    color: #ccc;\r\n                    font-size: 12px;\r\n                }\r\n                .wio-info-text-small {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 20px 0;\r\n                    font-size: 14px;\r\n                }\r\n                .wio-used-by-chars {\r\n                    margin-top: 5px;\r\n                    font-size: 12px;\r\n                    color: #aaa;\r\n                }\r\n                .wio-used-by-chars span {\r\n                    background: #555;\r\n                    padding: 2px 6px;\r\n                    border-radius: 3px;\r\n                    margin-right: 5px;\r\n                }\r\n                .wio-toast-notification {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-toast-notification.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-progress-toast {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-progress-toast.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-modal-overlay {\r\n                    position: fixed;\r\n                    top: 0;\r\n                    left: 0;\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    background: rgba(0,0,0,0.7);\r\n                    z-index: 10002;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    /* 移动端适配 */\r\n                    padding: env(safe-area-inset-top, 0) env(safe-area-inset-right, 0) env(safe-area-inset-bottom, 0) env(safe-area-inset-left, 0);\r\n                }\r\n                .wio-modal-content {\r\n                    background: #2a2a2a;\r\n                    border-radius: 8px;\r\n                    max-width: 500px;\r\n                    width: 90%;\r\n                    max-height: 80vh;\r\n                    max-height: 80dvh; /* 移动端使用dvh单位 */\r\n                    overflow-y: auto;\r\n                    /* 移动端安全区域 */\r\n                    margin: env(safe-area-inset-top, 0) env(safe-area-inset-right, 0) env(safe-area-inset-bottom, 0) env(safe-area-inset-left, 0);\r\n                }\r\n\r\n                /* 移动端模态框适配 */\r\n                @media (max-width: 768px) {\r\n                    .wio-modal-overlay {\r\n                        align-items: flex-start;\r\n                        padding-top: calc(env(safe-area-inset-top, 0) + 20px);\r\n                    }\r\n                    .wio-modal-content {\r\n                        width: 95%;\r\n                        max-height: 90dvh;\r\n                        margin-top: env(safe-area-inset-top, 0);\r\n                    }\r\n                }\r\n                .wio-modal-header {\r\n                    padding: 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    font-size: 18px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-body {\r\n                    padding: 20px;\r\n                    color: #ccc;\r\n                }\r\n                .wio-modal-input {\r\n                    width: 100%;\r\n                    padding: 10px;\r\n                    margin-top: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-input.wio-input-error {\r\n                    border-color: #dc3545;\r\n                    animation: shake 0.5s;\r\n                }\r\n                @keyframes shake {\r\n                    0%, 100% { transform: translateX(0); }\r\n                    25% { transform: translateX(-5px); }\r\n                    75% { transform: translateX(5px); }\r\n                }\r\n                .wio-modal-footer {\r\n                    padding: 20px;\r\n                    border-top: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    justify-content: flex-end;\r\n                }\r\n                .wio-modal-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-modal-ok {\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-ok:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-modal-cancel {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-cancel:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-drag-handle {\r\n                    cursor: grab;\r\n                    color: #ccc;\r\n                    margin-right: 10px;\r\n                    padding: 0 5px;\r\n                    opacity: 0.6;\r\n                    transition: opacity 0.2s;\r\n                }\r\n                .wio-drag-handle:hover {\r\n                    opacity: 1;\r\n                }\r\n                .wio-drag-handle:active {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-item-container.sortable-ghost {\r\n                    opacity: 0.4;\r\n                    background: #2a4a6b;\r\n                }\r\n                .wio-item-container.sortable-chosen {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-order-indicator {\r\n                    display: inline-block;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    font-size: 10px;\r\n                    font-weight: bold;\r\n                    padding: 2px 6px;\r\n                    border-radius: 10px;\r\n                    margin-right: 8px;\r\n                    min-width: 20px;\r\n                    text-align: center;\r\n                }\r\n                .wio-regex-list {\r\n                    padding: 15px;\r\n                }\r\n                /* 按钮激活状态 */\r\n                #${BUTTON_ID}.active {\r\n                    background-color: rgba(126, 183, 213, 0.3) !important;\r\n                    border-color: #7eb7d5 !important;\r\n                }\r\n                #${BUTTON_ID}:hover {\r\n                    background-color: rgba(126, 183, 213, 0.15);\r\n                }\r\n                /* 重命名UI样式 */\r\n                .wio-rename-ui {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    flex-grow: 1;\r\n                    margin-left: 10px;\r\n                }\r\n                .wio-rename-input-wrapper {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    flex-grow: 1;\r\n                    background-color: #2a2a2a;\r\n                    border-radius: 6px;\r\n                    padding: 4px;\r\n                    border: 2px solid #007bff;\r\n                    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\r\n                }\r\n                .wio-rename-input {\r\n                    flex-grow: 1;\r\n                    border: none;\r\n                    background: transparent;\r\n                    color: #fff;\r\n                    font-size: 14px;\r\n                    font-weight: 600;\r\n                    padding: 6px 8px;\r\n                    outline: none;\r\n                    min-width: 200px;\r\n                }\r\n                .wio-rename-input::placeholder {\r\n                    color: #888;\r\n                }\r\n                .wio-rename-buttons {\r\n                    display: flex;\r\n                    gap: 4px;\r\n                    margin-left: 8px;\r\n                }\r\n                .wio-rename-ui .wio-action-btn-icon {\r\n                    width: 28px;\r\n                    height: 28px;\r\n                    border-radius: 4px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    cursor: pointer;\r\n                    transition: all 0.2s;\r\n                    font-size: 12px;\r\n                }\r\n                .wio-rename-save-btn {\r\n                    background-color: #28a745;\r\n                    color: white;\r\n                }\r\n                .wio-rename-save-btn:hover {\r\n                    background-color: #218838;\r\n                }\r\n                .wio-rename-cancel-btn {\r\n                    background-color: #dc3545;\r\n                    color: white;\r\n                }\r\n                .wio-rename-cancel-btn:hover {\r\n                    background-color: #c82333;\r\n                }\r\n                .wio-item-container.renaming .wio-item-header {\r\n                    background-color: #2a4a6b;\r\n                }\r\n                /* 编辑器样式 */\r\n                .wio-collapsible-content {\r\n                    display: none;\r\n                    max-height: 45vh;\r\n                    max-height: 45dvh; /* 移动端使用dvh单位 */\r\n                    overflow-y: auto;\r\n                }\r\n                .wio-book-group .wio-collapsible-content {\r\n                    max-height: none;\r\n                    overflow: visible;\r\n                    padding-top: 10px;\r\n                    border-top: 1px solid #444;\r\n                }\r\n                .wio-editor-wrapper {\r\n                    padding: 15px;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    background-color: #2a2a2a;\r\n                    border-radius: 6px;\r\n                    margin: 10px;\r\n                }\r\n                .wio-editor-field {\r\n                    margin-top: 15px;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                }\r\n                .wio-editor-field:first-child {\r\n                    margin-top: 0;\r\n                }\r\n                .wio-editor-field label {\r\n                    font-weight: 600;\r\n                    margin-bottom: 5px;\r\n                    color: #ccc;\r\n                }\r\n                .wio-editor-field input[type=text],\r\n                .wio-editor-field textarea,\r\n                .wio-editor-field select,\r\n                .wio-editor-field .wio-edit-keys[contenteditable=\"true\"] {\r\n                    width: 100%;\r\n                    min-height: 35px;\r\n                    padding: 8px;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #555;\r\n                    background-color: #1a1a1a;\r\n                    color: #fff;\r\n                    font-family: inherit;\r\n                }\r\n                .wio-editor-field textarea {\r\n                    min-height: 80px;\r\n                    resize: vertical;\r\n                }\r\n                .wio-edit-content {\r\n                    min-height: 80px;\r\n                    padding: 8px;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #555;\r\n                    background-color: #1a1a1a;\r\n                    color: #fff;\r\n                    outline: none;\r\n                    white-space: pre-wrap; /* Linus's Fix: Let CSS handle newlines */\r\n                }\r\n                .wio-editor-group {\r\n                    margin-top: 20px;\r\n                }\r\n                .wio-editor-group h5 {\r\n                    margin: 0 0 10px 0;\r\n                    color: #007bff;\r\n                    font-size: 14px;\r\n                    font-weight: 600;\r\n                }\r\n                .wio-editor-grid {\r\n                    display: grid;\r\n                    grid-template-columns: 1fr 1fr 1fr;\r\n                    gap: 10px;\r\n                }\r\n                .wio-grid-item {\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                }\r\n                .wio-editor-options-row {\r\n                    display: flex;\r\n                    gap: 15px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-editor-option-item {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 5px;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                }\r\n                .wio-editor-actions {\r\n                    margin-top: 20px;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    justify-content: flex-end;\r\n                }\r\n                .wio-save-btn {\r\n                    background-color: #28a745;\r\n                    color: white;\r\n                    border: none;\r\n                    padding: 8px 16px;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-weight: 600;\r\n                }\r\n                .wio-save-btn:hover {\r\n                    background-color: #218838;\r\n                }\r\n                .wio-cancel-btn {\r\n                    background-color: #6c757d;\r\n                    color: white;\r\n                    border: none;\r\n                    padding: 8px 16px;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-weight: 600;\r\n                }\r\n                .wio-cancel-btn:hover {\r\n                    background-color: #5a6268;\r\n                }\r\n                /* 多选模式样式 */\r\n                .wio-multi-select-mode .wio-item-container,\r\n                .wio-multi-select-mode .wio-book-group,\r\n                .wio-book-group.editing-entries .wio-item-container {\r\n                    position: relative;\r\n                    cursor: pointer;\r\n                    transition: all 0.2s ease;\r\n                }\r\n                .wio-multi-select-mode .wio-item-container:hover,\r\n                .wio-multi-select-mode .wio-book-group:hover,\r\n                .wio-book-group.editing-entries .wio-item-container:hover {\r\n                    background-color: rgba(0, 123, 255, 0.1);\r\n                }\r\n                .wio-multi-select-mode .wio-item-container.selected,\r\n                .wio-multi-select-mode .wio-book-group.selected,\r\n                .wio-book-group.editing-entries .wio-item-container.selected {\r\n                    background-color: rgba(0, 123, 255, 0.2) !important;\r\n                    border-left: 4px solid #007bff;\r\n                    box-shadow: inset 0 0 0 1px rgba(0, 123, 255, 0.3);\r\n                }\r\n                .wio-multi-select-mode .wio-item-container.selected::before,\r\n                .wio-multi-select-mode .wio-book-group.selected::before,\r\n                .wio-book-group.editing-entries .wio-item-container.selected::before {\r\n                    content: \"✓\";\r\n                    position: absolute;\r\n                    left: 8px;\r\n                    top: 50%;\r\n                    transform: translateY(-50%);\r\n                    color: #007bff;\r\n                    font-weight: bold;\r\n                    font-size: 16px;\r\n                    z-index: 10;\r\n                }\r\n                .wio-multi-select-mode .wio-item-header,\r\n                .wio-multi-select-mode .wio-global-book-header,\r\n                .wio-book-group.editing-entries .wio-item-header {\r\n                    padding-left: 35px;\r\n                }\r\n                /* 多选控制面板 - 美化 */\r\n                #wio-multi-select-controls {\r\n                    background-color: #3a3a3a; /* 更柔和的背景 */\r\n                    border: 1px solid #4f4f4f; /* 添加边框 */\r\n                    border-radius: 6px; /* 圆角 */\r\n                    padding: 8px 12px; /* 调整内边距 */\r\n                    margin-left: 10px; /* 与主按钮的间距 */\r\n                    box-shadow: inset 0 1px 2px rgba(0,0,0,0.2); /* 内阴影增加深度 */\r\n                    flex-wrap: wrap;\r\n                    gap: 8px;\r\n                    align-items: center;\r\n                }\r\n                #wio-multi-select-controls .wio-selection-count {\r\n                    color: #ccc; /* 中性颜色 */\r\n                    font-weight: 600;\r\n                    margin-right: 15px;\r\n                }\r\n                #wio-multi-select-controls .wio-batch-btn {\r\n                    background-color: #495057;\r\n                    color: white;\r\n                    border: none;\r\n                    padding: 6px 12px;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background-color 0.2s;\r\n                }\r\n                #wio-multi-select-controls .wio-batch-btn:hover {\r\n                    background-color: #6c757d;\r\n                }\r\n                #wio-multi-select-controls .wio-batch-btn.danger {\r\n                    background-color: #dc3545;\r\n                }\r\n                #wio-multi-select-controls .wio-batch-btn.danger:hover {\r\n                    background-color: #c82333;\r\n                }\r\n                #wio-multi-select-controls .wio-batch-btn.success {\r\n                    background-color: #28a745;\r\n                }\r\n                #wio-multi-select-controls .wio-batch-btn.success:hover {\r\n                    background-color: #218838;\r\n                }\r\n                .wio-loading-container {\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    justify-content: center;\r\n                    align-items: center;\r\n                    height: 100%;\r\n                    padding: 20px;\r\n                    text-align: center;\r\n                }\r\n                .wio-loading-title {\r\n                    font-size: 18px;\r\n                    font-weight: 600;\r\n                    margin-bottom: 20px;\r\n                    color: #fff;\r\n                }\r\n                .wio-loading-progress-bar-container {\r\n                    width: 80%;\r\n                    max-width: 400px;\r\n                    height: 10px;\r\n                    background-color: #444;\r\n                    border-radius: 5px;\r\n                    overflow: hidden;\r\n                    margin-bottom: 15px;\r\n                }\r\n                .wio-loading-progress-bar {\r\n                    height: 100%;\r\n                    background-color: #007bff;\r\n                    border-radius: 5px;\r\n                    transition: width 0.3s ease;\r\n                }\r\n                .wio-loading-status-text {\r\n                    font-size: 14px;\r\n                    color: #ccc;\r\n                }\r\n            </style>\r\n        `;\n        $('head', parentDoc).append(basicStyles);\n    };\n    // --- 面板显示/隐藏函数 ---\n    const hidePanel = () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.hide();\n        $(`#${BUTTON_ID}`, parentDoc).removeClass('active');\n        $parentBody.off('mousedown.wio-outside-click');\n    };\n    const showPanel = async () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.css('display', 'flex');\n        $(`#${BUTTON_ID}`, parentDoc).addClass('active');\n        // 点击外部关闭面板\n        $parentBody.on('mousedown.wio-outside-click', function (event) {\n            if ($(event.target).closest(`#${PANEL_ID}`).length === 0 &&\n                $(event.target).closest(`#${BUTTON_ID}`).length === 0) {\n                hidePanel();\n            }\n        });\n        await loadAllData();\n    };\n    // --- 按钮事件处理函数 ---\n    const handleHeaderClick = errorCatched(async (event) => {\n        const $target = $(event.target);\n        const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');\n        // 如果点击的是按钮等可交互控件，则不执行后续逻辑\n        if ($target.closest('.wio-item-controls, .wio-rename-ui').length > 0) {\n            return;\n        }\n        // 如果处于全局多选模式，或者局部条目编辑模式\n        const isBookGroupEditing = $container.closest('.wio-book-group').hasClass('editing-entries');\n        if (appState.multiSelectMode || isBookGroupEditing) {\n            let itemKey;\n            const isGlobalLoreTab = appState.activeTab === 'global-lore';\n            const isBookHeader = $container.hasClass('wio-book-group');\n            // 全局多选模式下，且非局部编辑模式时，才可以选择 book header\n            if (appState.multiSelectMode && isGlobalLoreTab && isBookHeader && !isBookGroupEditing) {\n                const bookName = $container.data('book-name');\n                itemKey = `book:${bookName}`;\n            }\n            // 任何多选模式下都可以选择 item\n            else if ($container.hasClass('wio-item-container')) {\n                const itemType = $container.data('type');\n                const itemId = $container.data('id');\n                if (itemType === 'lore') {\n                    const bookName = $container.data('book-name');\n                    itemKey = `lore:${bookName}:${itemId}`;\n                }\n                else if (itemType === 'regex' && appState.multiSelectMode) {\n                    // 正则只能在全局多选下选择\n                    itemKey = `regex:${itemId}`;\n                }\n            }\n            if (itemKey) {\n                if (appState.selectedItems.has(itemKey)) {\n                    appState.selectedItems.delete(itemKey);\n                    $container.removeClass('selected');\n                }\n                else {\n                    appState.selectedItems.add(itemKey);\n                    $container.addClass('selected');\n                }\n                updateSelectionCount();\n            }\n            // 如果不是全局多选模式（即仅局部编辑），则不继续执行折叠逻辑\n            if (!appState.multiSelectMode) {\n                return;\n            }\n        }\n        // 非多选模式：展开/折叠内容\n        if ($container.hasClass('from-card') || $container.hasClass('renaming'))\n            return;\n        const $content = $container.find('.wio-collapsible-content').first();\n        // 对于世界书组，总是展开/折叠\n        if ($container.is('.wio-book-group')) {\n            $content.slideToggle(200);\n            return;\n        }\n        // 如果内容已经可见，则折叠\n        if ($content.is(':visible')) {\n            $content.slideUp(200, () => $content.empty());\n            return;\n        }\n        // 折叠其他同级项目\n        $container.siblings('.wio-item-container').find('.wio-collapsible-content:visible').slideUp(200).empty();\n        // 展开当前项目并加载内容\n        const type = $container.data('type');\n        const id = $container.data('id');\n        let item, editorHtml;\n        if (type === 'lore') {\n            const bookName = $container.data('book-name');\n            const entries = [...safeGetLorebookEntries(bookName)];\n            item = entries.find((e) => e.uid === id);\n            if (!item)\n                return;\n            // 获取搜索词以用于内容高亮\n            const searchTerm = $container.data('searchTerm') || '';\n            // 直接使用原始内容，不需要额外的unescapeHtml\n            const highlightedContent = highlightText(item.content || '', searchTerm);\n            const keywordsText = (item.keys || []).join(', ');\n            // 直接使用原始关键词，不需要额外的unescapeHtml\n            const highlightedKeywords = highlightText(keywordsText, searchTerm);\n            const positionOptions = Object.entries(LOREBOOK_OPTIONS.position)\n                .map(([value, text]) => `<option value=\"${value}\" ${item.position === value ? 'selected' : ''}>${text}</option>`)\n                .join('');\n            const logicOptions = Object.entries(LOREBOOK_OPTIONS.logic)\n                .map(([value, text]) => `<option value=\"${value}\" ${item.logic === value ? 'selected' : ''}>${text}</option>`)\n                .join('');\n            editorHtml = `\r\n        <div class=\"wio-editor-wrapper\">\r\n          <div class=\"wio-editor-field\">\r\n            <label>关键词 (逗号分隔)</label>\r\n            <div class=\"wio-edit-keys\" contenteditable=\"true\">${highlightedKeywords}</div>\r\n          </div>\r\n          <div class=\"wio-editor-field\">\r\n            <label>内容</label>\r\n            <div class=\"wio-edit-content\" contenteditable=\"true\">${highlightedContent}</div>\r\n          </div>\r\n          <div class=\"wio-editor-group\">\r\n            <h5>插入规则</h5>\r\n            <div class=\"wio-editor-grid\">\r\n              <div class=\"wio-grid-item\">\r\n                <label>位置</label>\r\n                <select class=\"wio-edit-position\">${positionOptions}</select>\r\n              </div>\r\n              <div class=\"wio-grid-item\">\r\n                <label>深度</label>\r\n                <input type=\"number\" class=\"wio-edit-depth\" placeholder=\"例如: 0\" value=\"${item.depth ?? ''}\">\r\n              </div>\r\n              <div class=\"wio-grid-item\">\r\n                <label>顺序</label>\r\n                <input type=\"number\" class=\"wio-edit-order\" placeholder=\"例如: 100\" value=\"${item.order ?? ''}\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-editor-group\">\r\n            <h5>激活逻辑</h5>\r\n            <div class=\"wio-editor-grid\">\r\n              <div class=\"wio-grid-item\">\r\n                <label>概率 (%)</label>\r\n                <input type=\"number\" class=\"wio-edit-probability\" min=\"0\" max=\"100\" placeholder=\"100\" value=\"${item.probability ?? ''}\">\r\n              </div>\r\n              <div class=\"wio-grid-item\">\r\n                <label>关键词逻辑</label>\r\n                <select class=\"wio-edit-logic\">${logicOptions}</select>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-editor-group\">\r\n            <h5>匹配与递归</h5>\r\n            <div class=\"wio-editor-options-row\">\r\n              <label class=\"wio-editor-option-item\">\r\n                <input type=\"checkbox\" class=\"wio-edit-case-sensitive\" ${item.case_sensitive ? 'checked' : ''}> 大小写敏感\r\n              </label>\r\n              <label class=\"wio-editor-option-item\">\r\n                <input type=\"checkbox\" class=\"wio-edit-match-whole\" ${item.match_whole_words ? 'checked' : ''}> 全词匹配\r\n              </label>\r\n              <label class=\"wio-editor-option-item\">\r\n                <input type=\"checkbox\" class=\"wio-edit-prevent-recursion\" ${item.prevent_recursion ? 'checked' : ''}> 防止递归\r\n              </label>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-editor-actions\">\r\n            <button class=\"wio-save-btn\">保存更改</button>\r\n            <button class=\"wio-cancel-btn\">取消</button>\r\n          </div>\r\n        </div>\r\n      `;\n        }\n        else {\n            // Regex editor\n            item = [...appState.regexes.global, ...appState.regexes.character].find((r) => r.id === id);\n            if (!item)\n                return;\n            editorHtml = `\r\n        <div class=\"wio-editor-wrapper\">\r\n          <div class=\"wio-editor-field\">\r\n            <label>查找正则表达式</label>\r\n            <textarea class=\"wio-edit-find\">${escapeHtml(item.find_regex || '')}</textarea>\r\n          </div>\r\n          <div class=\"wio-editor-field\">\r\n            <label>替换为</label>\r\n            <textarea class=\"wio-edit-replace\">${escapeHtml(item.replace_string || '')}</textarea>\r\n          </div>\r\n          <div class=\"wio-editor-actions\">\r\n            <button class=\"wio-save-btn\">保存更改</button>\r\n            <button class=\"wio-cancel-btn\">取消</button>\r\n          </div>\r\n        </div>\r\n      `;\n        }\n        $content.html(editorHtml).slideDown(200);\n    });\n    const handleToggleState = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $button = $(event.currentTarget);\n        const $elementToSort = $button.closest('.wio-book-group, .wio-item-container');\n        if ($elementToSort.hasClass('renaming'))\n            return;\n        const isEnabling = !$elementToSort.hasClass('enabled');\n        const parentList = $elementToSort.parent();\n        if ($button.hasClass('wio-global-toggle')) {\n            const bookName = $elementToSort.data('book-name');\n            const settings = await TavernAPI.getLorebookSettings();\n            const currentBooks = new Set(settings.selected_global_lorebooks || []);\n            if (isEnabling)\n                currentBooks.add(bookName);\n            else\n                currentBooks.delete(bookName);\n            await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(currentBooks) });\n            await TavernAPI.saveSettings();\n            const bookState = appState.allLorebooks.find(b => b.name === bookName);\n            if (bookState)\n                bookState.enabled = isEnabling;\n        }\n        else {\n            const type = $elementToSort.data('type');\n            const id = $elementToSort.data('id');\n            if (type === 'lore') {\n                const bookName = $elementToSort.data('book-name');\n                await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), enabled: isEnabling }]);\n                const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(id));\n                if (entry)\n                    entry.enabled = isEnabling;\n            }\n            else {\n                const allServerRegexes = await TavernAPI.getRegexes();\n                const regex = allServerRegexes.find((r) => r.id === id);\n                if (regex) {\n                    regex.enabled = isEnabling;\n                    await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));\n                    await TavernAPI.saveSettings();\n                    const localRegex = appState.regexes.global.find((r) => r.id === id) ||\n                        appState.regexes.character.find((r) => r.id === id);\n                    if (localRegex)\n                        localRegex.enabled = isEnabling;\n                }\n            }\n        }\n        showSuccessTick(isEnabling ? '已启用' : '已禁用');\n        $elementToSort.toggleClass('enabled', isEnabling);\n        // 重新排序：启用的项目排在前面\n        const items = parentList.children().get();\n        items.sort((a, b) => {\n            const aEnabled = $(a).hasClass('enabled');\n            const bEnabled = $(b).hasClass('enabled');\n            if (aEnabled !== bEnabled)\n                return bEnabled ? 1 : -1;\n            const aName = $(a).find('.wio-item-name').text().trim();\n            const bName = $(b).find('.wio-item-name').text().trim();\n            return aName.localeCompare(bName);\n        });\n        parentList.append(items);\n    });\n    const handleRename = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $container = $(event.currentTarget).closest('.wio-item-container');\n        if ($container.hasClass('renaming') || $container.length === 0)\n            return;\n        const $header = $container.find('.wio-item-header').first();\n        const $nameSpan = $header.find('.wio-item-name').first();\n        // 获取纯文本名称，排除子元素（如序号指示器）\n        let oldName = $nameSpan.text().trim();\n        // 如果有序号指示器，去掉它\n        const orderMatch = oldName.match(/^#\\d+\\s+(.+)$/);\n        if (orderMatch) {\n            oldName = orderMatch[1];\n        }\n        // 隐藏原始内容，显示编辑UI\n        $header.find('.wio-item-name, .wio-item-controls').hide();\n        const renameUIHtml = `\r\n      <div class=\"wio-rename-ui\">\r\n        <div class=\"wio-rename-input-wrapper\">\r\n          <input type=\"text\" class=\"wio-rename-input\" value=\"${escapeHtml(oldName)}\" />\r\n          <div class=\"wio-rename-buttons\">\r\n            <button class=\"wio-action-btn-icon wio-rename-save-btn\" title=\"确认\">\r\n              <i class=\"fa-solid fa-check\"></i>\r\n            </button>\r\n            <button class=\"wio-action-btn-icon wio-rename-cancel-btn\" title=\"取消\">\r\n              <i class=\"fa-solid fa-times\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\n        $container.addClass('renaming');\n        $header.append(renameUIHtml);\n        const $input = $header.find('.wio-rename-input');\n        $input.focus().select();\n        // 自动调整输入框宽度\n        $input.css('width', Math.max(200, oldName.length * 8 + 80) + 'px');\n    });\n    const exitRenameMode = ($container, newName = null) => {\n        const $header = $container.find('.wio-item-header').first();\n        const $nameSpan = $header.find('.wio-item-name').first();\n        if (newName) {\n            // 保留序号指示器，只更新名称部分\n            const $orderIndicator = $nameSpan.find('.wio-order-indicator');\n            if ($orderIndicator.length > 0) {\n                $nameSpan.html($orderIndicator[0].outerHTML + ' ' + escapeHtml(newName));\n            }\n            else {\n                $nameSpan.text(newName);\n            }\n        }\n        // 移除编辑UI，恢复原始控件\n        $header.find('.wio-rename-ui').remove();\n        $header.find('.wio-item-name, .wio-item-controls').show();\n        $container.removeClass('renaming');\n    };\n    const handleConfirmRename = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $container = $(event.currentTarget).closest('.wio-item-container');\n        const $input = $container.find('.wio-rename-input');\n        const newName = $input.val().trim();\n        const oldName = $container.find('.wio-item-name').first().text().trim();\n        if (!newName || newName === oldName) {\n            exitRenameMode($container, oldName);\n            return;\n        }\n        const type = $container.data('type');\n        const id = $container.data('id');\n        if (type === 'lore') {\n            const bookName = $container.data('book-name');\n            await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), comment: newName }]);\n            const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(id));\n            if (entry)\n                entry.comment = newName;\n        }\n        else {\n            // type === 'regex'\n            const allServerRegexes = await TavernAPI.getRegexes();\n            const regex = allServerRegexes.find((r) => r.id === id);\n            if (regex) {\n                regex.script_name = newName;\n                await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));\n                await TavernAPI.saveSettings();\n                const localRegex = appState.regexes.global.find((r) => r.id === id) ||\n                    appState.regexes.character.find((r) => r.id === id);\n                if (localRegex)\n                    localRegex.script_name = newName;\n            }\n        }\n        exitRenameMode($container, newName);\n        showSuccessTick('重命名成功');\n    });\n    const handleCancelRename = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $container = $(event.currentTarget).closest('.wio-item-container');\n        exitRenameMode($container);\n    });\n    const handleRenameKeydown = errorCatched(async (event) => {\n        if (event.key === 'Enter') {\n            $(event.currentTarget).siblings('.wio-rename-save-btn').click();\n        }\n        else if (event.key === 'Escape') {\n            $(event.currentTarget).siblings('.wio-rename-cancel-btn').click();\n        }\n    });\n    const handleDeleteEntry = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $item = $(event.currentTarget).closest('.wio-item-container');\n        const bookName = $item.data('book-name');\n        const uid = Number($item.data('id'));\n        const entryName = $item.find('.wio-item-name').text().trim();\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '确认删除',\n            text: `确定要删除条目\"${entryName}\"吗？此操作不可撤销。`,\n        });\n        if (confirmed) {\n            await TavernAPI.deleteLorebookEntries(bookName, [uid.toString()]);\n            const entries = safeGetLorebookEntries(bookName);\n            const index = entries.findIndex((e) => e.uid === uid);\n            if (index !== -1)\n                entries.splice(index, 1);\n            $item.remove();\n            showSuccessTick('条目已删除');\n        }\n    });\n    const handleDeleteBook = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $bookGroup = $(event.currentTarget).closest('.wio-book-group');\n        const bookName = $bookGroup.data('book-name');\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '确认删除',\n            text: `确定要删除世界书\"${bookName}\"吗？此操作不可撤销。`,\n        });\n        if (confirmed) {\n            await TavernAPI.deleteLorebook(bookName);\n            const index = appState.allLorebooks.findIndex(b => b.name === bookName);\n            if (index !== -1)\n                appState.allLorebooks.splice(index, 1);\n            appState.lorebookEntries.delete(bookName);\n            $bookGroup.remove();\n            showSuccessTick('世界书已删除');\n        }\n    });\n    const updateLinkedCharactersAndSettings = errorCatched(async (oldName, newName, progressToast) => {\n        const context = parentWin.SillyTavern.getContext() || {};\n        const allCharacters = Array.isArray(context.characters) ? context.characters : [];\n        const originalCharId = context.characterId;\n        progressToast.update('正在更新全局设置...');\n        const settings = await TavernAPI.getLorebookSettings();\n        const globalBooks = new Set(settings.selected_global_lorebooks || []);\n        if (globalBooks.has(oldName)) {\n            globalBooks.delete(oldName);\n            globalBooks.add(newName);\n            await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(globalBooks) });\n        }\n        if (appState.chatLorebook === oldName) {\n            progressToast.update('正在更新当前聊天设置...');\n            await TavernAPI.setChatLorebook(newName);\n            appState.chatLorebook = newName;\n        }\n        const linkedChars = appState.lorebookUsage.get(oldName) || [];\n        if (linkedChars.length > 0) {\n            for (let i = 0; i < linkedChars.length; i++) {\n                const charName = linkedChars[i];\n                const char = allCharacters.find((c) => c.name === charName);\n                if (!char)\n                    continue;\n                progressToast.update(`正在更新角色: ${charName} (${i + 1}/${linkedChars.length})`);\n                // 切换到目标角色以修改其数据\n                await context.selectCharacterById(char.avatar);\n                const charBooks = await TavernAPI.getCurrentCharLorebooks();\n                let updated = false;\n                if (charBooks.primary === oldName) {\n                    charBooks.primary = newName;\n                    updated = true;\n                }\n                if (charBooks.additional && charBooks.additional.includes(oldName)) {\n                    charBooks.additional = charBooks.additional.map((book) => (book === oldName ? newName : book));\n                    updated = true;\n                }\n                if (updated) {\n                    await TavernAPI.setCurrentCharLorebooks(charBooks);\n                }\n            }\n        }\n        // 切换回原始角色\n        if (originalCharId !== undefined && context.characterId !== originalCharId) {\n            progressToast.update('正在恢复原始角色...');\n            await context.selectCharacterById(originalCharId);\n        }\n        // 更新本地的 aopState.lorebookUsage 缓存\n        if (appState.lorebookUsage.has(oldName)) {\n            const chars = appState.lorebookUsage.get(oldName);\n            appState.lorebookUsage.delete(oldName);\n            appState.lorebookUsage.set(newName, chars);\n        }\n    });\n    const handleRenameBook = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $bookGroup = $(event.currentTarget).closest('.wio-book-group');\n        const oldName = $bookGroup.data('book-name');\n        if (!oldName)\n            return;\n        const newName = await showModal({\n            type: 'prompt',\n            title: '重命名世界书',\n            text: '请输入新的世界书名称：',\n            value: oldName,\n        });\n        if (newName && newName.trim() && newName.trim() !== oldName) {\n            const finalNewName = newName.trim();\n            const linkedChars = appState.lorebookUsage.get(oldName) || [];\n            let confirmText = `确定要将世界书 \"${oldName}\" 重命名为 \"${finalNewName}\" 吗？`;\n            if (linkedChars.length > 0) {\n                confirmText += `\\n\\n警告：此操作将会临时切换 ${linkedChars.length} 个关联角色的界面来更新其设置，过程可能会有视觉上的闪烁。请在操作完成前不要进行其他操作。`;\n            }\n            const confirmed = await showModal({\n                type: 'confirm',\n                title: '确认重命名',\n                text: confirmText,\n            });\n            if (!confirmed)\n                return;\n            const progressToast = showProgressToast('正在开始重命名...');\n            try {\n                progressToast.update('步骤 1/4: 创建新的世界书...');\n                await TavernAPI.createLorebook(finalNewName);\n                const entries = safeGetLorebookEntries(oldName);\n                if (entries.length > 0) {\n                    progressToast.update('步骤 2/4: 复制条目...');\n                    await TavernAPI.createLorebookEntries(finalNewName, entries);\n                }\n                safeSetLorebookEntries(finalNewName, entries);\n                progressToast.update('步骤 3/4: 更新关联的角色与设置...');\n                await updateLinkedCharactersAndSettings(oldName, finalNewName, progressToast);\n                progressToast.update('步骤 4/4: 删除旧的世界书...');\n                await TavernAPI.deleteLorebook(oldName);\n                // 更新本地状态\n                const bookState = appState.allLorebooks.find(b => b.name === oldName);\n                if (bookState) {\n                    bookState.name = finalNewName;\n                }\n                safeDeleteLorebookEntries(oldName);\n                progressToast.remove();\n                showSuccessTick('世界书重命名成功');\n                await loadAllData(); // 使用loadAllData以保证状态完全同步\n            }\n            catch (error) {\n                progressToast.remove();\n                console.error('[WorldInfoOptimizer] Rename book error:', error);\n                await showModal({\n                    type: 'alert',\n                    title: '重命名失败',\n                    text: '操作过程中发生错误，部分更改可能未生效。建议刷新数据。请检查开发者控制台获取详细信息。',\n                });\n                await loadAllData();\n            }\n        }\n    });\n    const handleCreateEntry = errorCatched(async (event) => {\n        const bookName = $(event.currentTarget).data('book-name');\n        const entryName = await showModal({\n            type: 'prompt',\n            title: '新建条目',\n            text: '请输入条目名称：',\n            value: '新条目',\n        });\n        if (entryName) {\n            const newEntry = {\n                uid: Date.now().toString(),\n                comment: entryName,\n                keys: [entryName],\n                content: '',\n                enabled: true,\n                insertion_order: 100,\n                case_sensitive: false,\n                name: entryName,\n                priority: 400,\n                id: Date.now(),\n                key: [entryName],\n                keysecondary: [],\n                selective: true,\n                constant: false,\n                vectorized: false,\n                selectiveLogic: 0,\n                addMemo: false,\n                order: 100,\n                position: 0,\n                disable: false,\n                excludeRecursion: false,\n                delayUntilRecursion: false,\n                display_index: 0,\n                forceActivation: false,\n                automationId: '',\n                role: 0,\n                scanDepth: null,\n                caseSensitive: false,\n                matchWholeWords: false,\n                useGroupScoring: false,\n                groupOverride: false,\n                groupWeight: 100,\n                sticky: 0,\n                cooldown: 0,\n                delay: 0,\n            };\n            await TavernAPI.createLorebookEntries(bookName, [newEntry]);\n            const entries = safeGetLorebookEntries(bookName);\n            entries.push(newEntry);\n            showSuccessTick('条目创建成功');\n            renderContent();\n        }\n    });\n    const handleBatchSetRecursion = errorCatched(async (event) => {\n        const bookName = $(event.currentTarget).data('book-name');\n        const entries = [...safeGetLorebookEntries(bookName)];\n        if (!entries || entries.length === 0) {\n            await showModal({ type: 'alert', title: '提示', text: '该世界书没有条目可操作。' });\n            return;\n        }\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '批量设置防递归',\n            text: `确定要为\"${bookName}\"中的所有 ${entries.length} 个条目开启\"防止递归\"吗？`,\n        });\n        if (confirmed) {\n            const updatedEntries = entries.map((entry) => ({\n                uid: entry.uid,\n                excludeRecursion: true,\n            }));\n            await TavernAPI.setLorebookEntries(bookName, updatedEntries);\n            // 更新本地状态\n            entries.forEach((entry) => {\n                entry.excludeRecursion = true;\n            });\n            showSuccessTick('已为所有条目开启\"防止递归\"');\n        }\n    });\n    const handleFixKeywords = errorCatched(async (event) => {\n        const bookName = $(event.currentTarget).data('book-name');\n        const entries = [...safeGetLorebookEntries(bookName)];\n        if (!entries || entries.length === 0) {\n            await showModal({ type: 'alert', title: '提示', text: '该世界书没有条目可操作。' });\n            return;\n        }\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '修复关键词',\n            text: `确定要修复\"${bookName}\"中所有条目的关键词格式吗？这将清理重复和空白关键词。`,\n        });\n        if (confirmed) {\n            let fixedCount = 0;\n            const updatedEntries = entries\n                .map((entry) => {\n                const originalKeys = entry.keys || [];\n                const cleanedKeys = [\n                    ...new Set(originalKeys.map((key) => key.trim()).filter((key) => key.length > 0)),\n                ];\n                if (JSON.stringify(originalKeys) !== JSON.stringify(cleanedKeys)) {\n                    fixedCount++;\n                    return {\n                        uid: entry.uid,\n                        keys: cleanedKeys,\n                    };\n                }\n                return null;\n            })\n                .filter(Boolean);\n            if (updatedEntries.length > 0) {\n                await TavernAPI.setLorebookEntries(bookName, updatedEntries);\n                // 更新本地状态\n                entries.forEach((entry) => {\n                    const update = updatedEntries.find((u) => u.uid === entry.uid);\n                    if (update) {\n                        entry.keys = update.keys;\n                    }\n                });\n            }\n            showSuccessTick(`已修复 ${fixedCount} 个条目的关键词`);\n        }\n    });\n    const handleEditEntriesToggle = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $button = $(event.currentTarget);\n        const $bookGroup = $button.closest('.wio-book-group');\n        const isEditing = $bookGroup.hasClass('editing-entries');\n        if (isEditing) {\n            $bookGroup.removeClass('editing-entries');\n            $button.attr('title', '多选条目').find('i').removeClass('fa-check-square').addClass('fa-list-check');\n            $button.removeClass('active');\n        }\n        else {\n            $bookGroup.addClass('editing-entries');\n            $button.attr('title', '完成选择').find('i').removeClass('fa-list-check').addClass('fa-check-square');\n            $button.addClass('active');\n            // 主动打开条目列表\n            $bookGroup.find('.wio-collapsible-content').slideDown(200);\n        }\n        // 更新多选控件的可见性\n        const isAnyEditing = appState.multiSelectMode || $('.wio-book-group.editing-entries', parentWin.document).length > 0;\n        $(`#wio-multi-select-controls`, parentWin.document).toggle(isAnyEditing);\n    });\n    const handleCollapseAll = errorCatched(async () => {\n        const parentDoc = parentWin.document;\n        const $allCollapsible = $(`#${PANEL_ID} .wio-collapsible-content`, parentDoc);\n        $allCollapsible.slideUp(200);\n        $allCollapsible.closest('.wio-item-container, .wio-book-group').addClass('collapsed');\n        showSuccessTick('已折叠所有项目');\n    });\n    const handleCollapseCurrent = errorCatched(async () => {\n        const parentDoc = parentWin.document;\n        const activeTab = appState.activeTab;\n        const $currentTabContent = $(`#${PANEL_ID} .wio-tab-content[data-tab=\"${activeTab}\"] .wio-collapsible-content`, parentDoc);\n        $currentTabContent.slideUp(200);\n        $currentTabContent.closest('.wio-item-container, .wio-book-group').addClass('collapsed');\n        showSuccessTick('已折叠当前标签页的所有项目');\n    });\n    const handleSaveEditor = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $editor = $(event.currentTarget).closest('.wio-editor-wrapper');\n        const $container = $editor.closest('.wio-item-container');\n        const type = $container.data('type');\n        const id = $container.data('id');\n        if (type === 'lore') {\n            const bookName = $container.data('book-name');\n            const entries = [...safeGetLorebookEntries(bookName)];\n            const entry = entries.find((e) => e.uid === id);\n            if (!entry)\n                return;\n            // 收集编辑器中的数据\n            const updatedData = {\n                uid: entry.uid,\n                keys: $editor\n                    .find('.wio-edit-keys')\n                    .text() // 使用 .text() 从 contenteditable div 获取纯文本\n                    .toString()\n                    .split(',')\n                    .map((k) => k.trim())\n                    .filter((k) => k),\n                content: $editor.find('.wio-edit-content').text() || '',\n                position: parseInt($editor.find('.wio-edit-position').val().toString()) || 0,\n                depth: parseInt($editor.find('.wio-edit-depth').val().toString()) || 0,\n                order: parseInt($editor.find('.wio-edit-order').val().toString()) || 100,\n                probability: parseInt($editor.find('.wio-edit-probability').val().toString()) || 100,\n                logic: parseInt($editor.find('.wio-edit-logic').val().toString()) || 0,\n                case_sensitive: $editor.find('.wio-edit-case-sensitive').is(':checked'),\n                match_whole_words: $editor.find('.wio-edit-match-whole').is(':checked'),\n                prevent_recursion: $editor.find('.wio-edit-prevent-recursion').is(':checked'),\n            };\n            // 保存到服务器\n            await TavernAPI.setLorebookEntries(bookName, [updatedData]);\n            // 更新本地状态\n            Object.assign(entry, updatedData);\n            showSuccessTick('条目已保存');\n        }\n        else {\n            // Regex editor\n            const allServerRegexes = await TavernAPI.getRegexes();\n            const regex = allServerRegexes.find((r) => r.id === id);\n            if (!regex)\n                return;\n            regex.find_regex = $editor.find('.wio-edit-find').val().toString();\n            regex.replace_string = $editor.find('.wio-edit-replace').val().toString();\n            await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));\n            await TavernAPI.saveSettings();\n            // 更新本地状态\n            const localRegex = appState.regexes.global.find((r) => r.id === id) ||\n                appState.regexes.character.find((r) => r.id === id);\n            if (localRegex) {\n                localRegex.find_regex = regex.find_regex;\n                localRegex.replace_string = regex.replace_string;\n            }\n            showSuccessTick('正则已保存');\n        }\n        // 关闭编辑器\n        $container.find('.wio-collapsible-content').slideUp(200, () => {\n            $container.find('.wio-collapsible-content').empty();\n        });\n    });\n    const handleCancelEditor = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $container = $(event.currentTarget).closest('.wio-item-container');\n        // 关闭编辑器\n        $container.find('.wio-collapsible-content').slideUp(200, () => {\n            $container.find('.wio-collapsible-content').empty();\n        });\n    });\n    // --- 多选批量操作函数 ---\n    const updateSelectionCount = () => {\n        const count = appState.selectedItems.size;\n        const parentDoc = parentWin.document;\n        $(`#wio-selection-count`, parentDoc).text(`已选择: ${count}`);\n    };\n    const handleSelectAll = errorCatched(async () => {\n        const parentDoc = parentWin.document;\n        const $visibleItems = $(`#${PANEL_ID} .wio-item-container:visible, #${PANEL_ID} .wio-book-group:visible`, parentDoc);\n        $visibleItems.each((_, element) => {\n            const $item = $(element);\n            const itemKey = getItemKey($item);\n            if (itemKey && canSelectItem($item)) {\n                appState.selectedItems.add(itemKey);\n                $item.addClass('selected');\n            }\n        });\n        updateSelectionCount();\n        showSuccessTick('已全选当前页面的所有项目');\n    });\n    const handleDeselectAll = errorCatched(async () => {\n        const parentDoc = parentWin.document;\n        appState.selectedItems.clear();\n        $(`#${PANEL_ID} .selected`, parentDoc).removeClass('selected');\n        updateSelectionCount();\n        showSuccessTick('已取消全选');\n    });\n    const handleInvertSelection = errorCatched(async () => {\n        const parentDoc = parentWin.document;\n        const $visibleItems = $(`#${PANEL_ID} .wio-item-container:visible, #${PANEL_ID} .wio-book-group:visible`, parentDoc);\n        $visibleItems.each((_, element) => {\n            const $item = $(element);\n            const itemKey = getItemKey($item);\n            if (itemKey && canSelectItem($item)) {\n                if (appState.selectedItems.has(itemKey)) {\n                    appState.selectedItems.delete(itemKey);\n                    $item.removeClass('selected');\n                }\n                else {\n                    appState.selectedItems.add(itemKey);\n                    $item.addClass('selected');\n                }\n            }\n        });\n        updateSelectionCount();\n        showSuccessTick('已反选');\n    });\n    const getItemKey = ($item) => {\n        const isGlobalLoreTab = appState.activeTab === 'global-lore';\n        const isBookHeader = $item.hasClass('wio-book-group');\n        if (isGlobalLoreTab && isBookHeader) {\n            const isEditingEntries = $item.hasClass('editing-entries');\n            if (!isEditingEntries) {\n                const bookName = $item.data('book-name');\n                return `book:${bookName}`;\n            }\n        }\n        else if ($item.hasClass('wio-item-container')) {\n            const canSelectItem = isGlobalLoreTab ? $item.closest('.wio-book-group').hasClass('editing-entries') : true;\n            if (canSelectItem) {\n                const itemType = $item.data('type');\n                const itemId = $item.data('id');\n                if (itemType === 'lore') {\n                    const bookName = $item.data('book-name');\n                    return `lore:${bookName}:${itemId}`;\n                }\n                else {\n                    return `regex:${itemId}`;\n                }\n            }\n        }\n        return null;\n    };\n    const canSelectItem = ($item) => {\n        const isGlobalLoreTab = appState.activeTab === 'global-lore';\n        const isBookHeader = $item.hasClass('wio-book-group');\n        if (isGlobalLoreTab && isBookHeader) {\n            return !$item.hasClass('editing-entries');\n        }\n        else if ($item.hasClass('wio-item-container')) {\n            return isGlobalLoreTab ? $item.closest('.wio-book-group').hasClass('editing-entries') : true;\n        }\n        return false;\n    };\n    const handleBatchEnable = errorCatched(async () => {\n        const selectedItems = Array.from(appState.selectedItems);\n        if (selectedItems.length === 0) {\n            await showModal({ type: 'alert', title: '提示', text: '请先选择要启用的项目。' });\n            return;\n        }\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '批量启用',\n            text: `确定要启用选中的 ${selectedItems.length} 个项目吗？`,\n        });\n        if (confirmed) {\n            let successCount = 0;\n            for (const itemKey of selectedItems) {\n                try {\n                    if (itemKey.startsWith('book:')) {\n                        const bookName = itemKey.substring(5);\n                        const settings = await TavernAPI.getLorebookSettings();\n                        const currentBooks = new Set(settings.selected_global_lorebooks || []);\n                        currentBooks.add(bookName);\n                        await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(currentBooks) });\n                        const bookState = appState.allLorebooks.find(b => b.name === bookName);\n                        if (bookState)\n                            bookState.enabled = true;\n                    }\n                    else if (itemKey.startsWith('lore:')) {\n                        const [, bookName, uid] = itemKey.split(':');\n                        await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(uid), enabled: true }]);\n                        const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(uid));\n                        if (entry)\n                            entry.enabled = true;\n                    }\n                    else if (itemKey.startsWith('regex:')) {\n                        const id = itemKey.substring(6);\n                        const allServerRegexes = await TavernAPI.getRegexes();\n                        const regex = allServerRegexes.find((r) => r.id === id);\n                        if (regex) {\n                            regex.enabled = true;\n                            await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));\n                            const localRegex = appState.regexes.global.find((r) => r.id === id) ||\n                                appState.regexes.character.find((r) => r.id === id);\n                            if (localRegex)\n                                localRegex.enabled = true;\n                        }\n                    }\n                    successCount++;\n                }\n                catch (error) {\n                    console.error(`Failed to enable item ${itemKey}:`, error);\n                }\n            }\n            await TavernAPI.saveSettings();\n            showSuccessTick(`已启用 ${successCount} 个项目`);\n            renderContent();\n        }\n    });\n    const handleBatchDisable = errorCatched(async () => {\n        const selectedItems = Array.from(appState.selectedItems);\n        if (selectedItems.length === 0) {\n            await showModal({ type: 'alert', title: '提示', text: '请先选择要禁用的项目。' });\n            return;\n        }\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '批量禁用',\n            text: `确定要禁用选中的 ${selectedItems.length} 个项目吗？`,\n        });\n        if (confirmed) {\n            let successCount = 0;\n            for (const itemKey of selectedItems) {\n                try {\n                    if (itemKey.startsWith('book:')) {\n                        const bookName = itemKey.substring(5);\n                        const settings = await TavernAPI.getLorebookSettings();\n                        const currentBooks = new Set(settings.selected_global_lorebooks || []);\n                        currentBooks.delete(bookName);\n                        await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(currentBooks) });\n                        const bookState = appState.allLorebooks.find(b => b.name === bookName);\n                        if (bookState)\n                            bookState.enabled = false;\n                    }\n                    else if (itemKey.startsWith('lore:')) {\n                        const [, bookName, uid] = itemKey.split(':');\n                        await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(uid), enabled: false }]);\n                        const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(uid));\n                        if (entry)\n                            entry.enabled = false;\n                    }\n                    else if (itemKey.startsWith('regex:')) {\n                        const id = itemKey.substring(6);\n                        const allServerRegexes = await TavernAPI.getRegexes();\n                        const regex = allServerRegexes.find((r) => r.id === id);\n                        if (regex) {\n                            regex.enabled = false;\n                            await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));\n                            const localRegex = appState.regexes.global.find((r) => r.id === id) ||\n                                appState.regexes.character.find((r) => r.id === id);\n                            if (localRegex)\n                                localRegex.enabled = false;\n                        }\n                    }\n                    successCount++;\n                }\n                catch (error) {\n                    console.error(`Failed to disable item ${itemKey}:`, error);\n                }\n            }\n            await TavernAPI.saveSettings();\n            showSuccessTick(`已禁用 ${successCount} 个项目`);\n            renderContent();\n        }\n    });\n    const handleBatchDelete = errorCatched(async () => {\n        const selectedItems = Array.from(appState.selectedItems);\n        if (selectedItems.length === 0) {\n            await showModal({ type: 'alert', title: '提示', text: '请先选择要删除的项目。' });\n            return;\n        }\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '批量删除',\n            text: `确定要删除选中的 ${selectedItems.length} 个项目吗？此操作不可撤销。`,\n        });\n        if (confirmed) {\n            let successCount = 0;\n            for (const itemKey of selectedItems) {\n                try {\n                    if (itemKey.startsWith('book:')) {\n                        const bookName = itemKey.substring(5);\n                        await TavernAPI.deleteLorebook(bookName);\n                        const index = appState.allLorebooks.findIndex(b => b.name === bookName);\n                        if (index !== -1)\n                            appState.allLorebooks.splice(index, 1);\n                        appState.lorebookEntries.delete(bookName);\n                    }\n                    else if (itemKey.startsWith('lore:')) {\n                        const [, bookName, uid] = itemKey.split(':');\n                        await TavernAPI.deleteLorebookEntries(bookName, [uid]);\n                        const entries = safeGetLorebookEntries(bookName);\n                        const index = entries.findIndex((e) => e.uid === Number(uid));\n                        if (index !== -1)\n                            entries.splice(index, 1);\n                    }\n                    // 注意：正则表达式通常不支持批量删除，因为它们可能来自角色卡\n                    successCount++;\n                }\n                catch (error) {\n                    console.error(`Failed to delete item ${itemKey}:`, error);\n                }\n            }\n            showSuccessTick(`已删除 ${successCount} 个项目`);\n            appState.selectedItems.clear();\n            renderContent();\n        }\n    });\n    // --- 事件处理器 ---\n    const bindEventHandlers = () => {\n        const parentDoc = parentWin.document;\n        // 扩展菜单按钮点击事件\n        $(parentDoc).on('click', `#${BUTTON_ID}`, async () => {\n            const $panel = $(`#${PANEL_ID}`, parentDoc);\n            if ($panel.is(':visible')) {\n                hidePanel();\n            }\n            else {\n                await showPanel();\n            }\n        });\n        // 面板关闭按钮\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {\n            hidePanel();\n        });\n        // 刷新按钮\n        $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {\n            loadAllData();\n        });\n        // 标签页切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, (event) => {\n            const $this = $(event.currentTarget);\n            const tabId = $this.data('tab');\n            $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');\n            $this.addClass('active');\n            appState.activeTab = tabId;\n            renderContent();\n        });\n        // 搜索输入\n        $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {\n            renderContent();\n        });\n        // 搜索过滤器\n        $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type=\"checkbox\"]`, () => {\n            renderContent();\n        });\n        // 替换按钮\n        $(parentDoc).on('click', '#wio-replace-btn', () => {\n            handleReplace();\n        });\n        // 新建世界书按钮\n        $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {\n            try {\n                const bookName = await showModal({\n                    type: 'prompt',\n                    title: '新建世界书',\n                    text: '请输入世界书名称：',\n                    placeholder: '世界书名称',\n                });\n                if (bookName && typeof bookName === 'string') {\n                    const progressToast = showProgressToast('正在创建世界书...');\n                    await TavernAPI.createLorebook(bookName.trim());\n                    progressToast.remove();\n                    showSuccessTick(`世界书 \"${bookName}\" 创建成功`);\n                    loadAllData(); // 重新加载数据\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error creating lorebook:', error);\n            }\n        });\n        // 全部折叠按钮\n        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {\n            $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');\n        });\n        // 多选模式切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, (event) => {\n            appState.multiSelectMode = !appState.multiSelectMode;\n            if (!appState.multiSelectMode) {\n                appState.selectedItems.clear();\n            }\n            renderContent();\n        });\n        // ESC键关闭面板\n        $(parentDoc).on('keydown', (e) => {\n            if (e.key === 'Escape') {\n                const $panel = $(`#${PANEL_ID}`, parentDoc);\n                if ($panel.is(':visible')) {\n                    hidePanel();\n                }\n            }\n        });\n        // 条目和世界书操作按钮事件\n        $(parentDoc).on('click', '.wio-item-header, .wio-global-book-header', handleHeaderClick);\n        $(parentDoc).on('click', '.wio-item-toggle, .wio-global-toggle', handleToggleState);\n        $(parentDoc).on('click', '.wio-rename-btn', handleRename);\n        $(parentDoc).on('click', '.wio-rename-save-btn', handleConfirmRename);\n        $(parentDoc).on('click', '.wio-rename-cancel-btn', handleCancelRename);\n        $(parentDoc).on('keydown', '.wio-rename-input', handleRenameKeydown);\n        $(parentDoc).on('click', '.wio-delete-entry-btn', handleDeleteEntry);\n        $(parentDoc).on('click', '.wio-delete-book-btn', handleDeleteBook);\n        $(parentDoc).on('click', '.wio-rename-book-btn', handleRenameBook);\n        $(parentDoc).on('click', '.wio-create-entry-btn', handleCreateEntry);\n        $(parentDoc).on('click', '.wio-batch-recursion-btn', handleBatchSetRecursion);\n        $(parentDoc).on('click', '.wio-fix-keywords-btn', handleFixKeywords);\n        $(parentDoc).on('click', '.wio-edit-entries-btn', handleEditEntriesToggle);\n        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, handleCollapseAll);\n        $(parentDoc).on('click', `#${COLLAPSE_CURRENT_BTN_ID}`, handleCollapseCurrent);\n        $(parentDoc).on('click', '.wio-save-btn', handleSaveEditor);\n        $(parentDoc).on('click', '.wio-cancel-btn', handleCancelEditor);\n        // 多选批量操作按钮\n        $(parentDoc).on('click', '#wio-select-all-btn', handleSelectAll);\n        $(parentDoc).on('click', '#wio-select-none-btn', handleDeselectAll);\n        $(parentDoc).on('click', '#wio-select-invert-btn', handleInvertSelection);\n        $(parentDoc).on('click', '#wio-batch-enable-btn', handleBatchEnable);\n        $(parentDoc).on('click', '#wio-batch-disable-btn', handleBatchDisable);\n        $(parentDoc).on('click', '#wio-batch-delete-btn', handleBatchDelete);\n        console.log('[WorldInfoOptimizer] Event handlers bound successfully.');\n    };\n    // --- 初始化脚本 ---\n    console.log('[WorldInfoOptimizer] Starting initialization...');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/index.ts\n\n}");
  }
};

var __webpack_exports__ = {};

__webpack_modules__["./src/WorldInfoOptimizer/index.ts"]();